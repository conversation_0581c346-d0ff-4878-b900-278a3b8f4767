# Commande de Vérification de Bug

Vérifiez que la correction du bug fonctionne correctement et n’introduit pas de régressions.

## Utilisation
```
/bug-verify [nom-du-bug]
```

## Vue d’ensemble de la phase
**Votre rôle** : Vérifier minutieusement que la correction fonctionne et documenter les résultats

Ceci est la Phase 4 (finale) du workflow de correction de bug. Votre objectif est de confirmer que le bug est résolu et que la correction est sûre.

## Instructions

Vous travaillez sur la phase de vérification du workflow de correction de bug.

1. **Prérequis & Chargement du contexte**
   - Assurez-vous que la correction a été implémentée

   **Charger TOUT le contexte une fois (Chargement hiérarchique du contexte) :**
   ```bash
   # Charger les modèles de bug pour la structure de vérification
   claude-code-spec-workflow get-template-context bug
   ```

   **Documents de bug à lire directement :**
   - `.sdd/bugs/{nom-du-bug}/report.md`
   - `.sdd/bugs/{nom-du-bug}/analysis.md`
   - Comprendre ce qui a été changé et pourquoi
   - Avoir le plan de vérification depuis analysis.md

2. **Processus de vérification**
   1. **Test du bug original**
      - Reproduire les étapes originales depuis report.md
      - Vérifier que le bug ne se produit plus
      - Tester les cas limites mentionnés dans l’analyse

   2. **Test de régression**
      - Tester les fonctionnalités liées
      - Vérifier qu’aucun nouveau bug n’a été introduit
      - Vérifier les points d’intégration
      - Exécuter les tests automatisés si disponibles

   3. **Vérification de la qualité du code**
      - Revoir les changements de code pour la qualité
      - Vérifier le respect des standards du projet
      - Vérifier que la gestion des erreurs est appropriée
      - S’assurer que les tests sont adéquats

3. **Liste de vérification de la vérification**
   - **Problème original** : Les étapes de reproduction du bug ne causent plus le problème
   - **Fonctionnalités liées** : Pas de régression dans les fonctionnalités associées
   - **Cas limites** : Les conditions de frontière fonctionnent correctement
   - **Gestion des erreurs** : Les erreurs sont gérées correctement
   - **Tests** : Tous les tests passent, de nouveaux tests ajoutés pour prévenir les régressions
   - **Qualité du code** : Les changements suivent les conventions du projet

4. **Créer le document de vérification**
   - **Modèle à suivre** : Utilisez précisément le modèle de vérification de bug chargé précédemment (ne pas recharger)
   - Documentez tous les résultats des tests en suivant la structure du modèle de vérification de bug

## Utilisation du modèle
- **Suivre la structure exacte** : Utilisez précisément le modèle de vérification chargé
- **Inclure toutes les sections** : N’omettez aucune section requise du modèle
- **Compléter la liste de vérification** : Suivez le format de la liste de vérification du modèle pour plus de rigueur

5. **Approbation finale**
   - Présentez les résultats complets de la vérification (manuelle et automatisée si disponible)
   - Montrez que toutes les vérifications sont validées
   - Demandez : « La correction du bug a été vérifiée avec succès. Ce bug est-il résolu ? »
   - Obtenez la confirmation finale avant de clôturer

## Directives de vérification

### Approche de test
- Testez le scénario exact du rapport de bug
- Vérifiez que la correction fonctionne dans différents environnements
- Vérifiez que les fonctionnalités associées fonctionnent toujours
- Testez les conditions d’erreur et les cas limites

### Vérification de la qualité
- Le code respecte les standards du projet
- Une gestion appropriée des erreurs a été ajoutée
- Pas d’implications de sécurité
- Les performances ne sont pas impactées négativement

### Vérification de la documentation
- Les commentaires du code sont mis à jour si nécessaire
- Toute documentation pertinente reflète les changements
- La correction du bug est documentée de manière appropriée

## Critères d’achèvement

La correction du bug est complète lorsque :
- ✅ Le bug original ne se produit plus
- ✅ Aucune régression n’a été introduite
- ✅ Tous les tests passent
- ✅ Le code respecte les standards du projet
- ✅ La documentation est à jour
- ✅ L’utilisateur confirme la résolution

## Règles critiques
- **TESTEZ MINUTIEUSEMENT** le scénario du bug original
- **VÉRIFIEZ** qu’il n’y a pas de régression dans les fonctionnalités associées
- **DOCUMENTEZ** tous les résultats de vérification
- **OBTENEZ** l’approbation finale de l’utilisateur avant de considérer le bug comme résolu

## Critères de succès
Une correction de bug réussie inclut :
- ✅ Cause racine identifiée et traitée
- ✅ Correction minimale et ciblée implémentée
- ✅ Vérification complète effectuée
- ✅ Aucune régression introduite
- ✅ Tests appropriés ajoutés
- ✅ L’utilisateur confirme que le problème est résolu
