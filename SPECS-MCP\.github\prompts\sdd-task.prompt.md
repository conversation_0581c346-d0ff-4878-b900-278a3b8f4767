---
mode: agent
model: <PERSON> 4
tools: ['codebase', 'search', 'usages', 'terminal']
description: Exécute une tâche d'implémentation depuis tasks.md, met à jour le fichier automatiquement, et s'arrête pour validation.
---

# Exécution des Tâches SDD

Tu vas maintenant exécuter une tâche d'implémentation depuis le fichier tasks.md selon la méthodologie Spec-Driven Development.

## Objectif

Exécuter une seule tâche à la fois depuis le plan d'implémentation, mettre à jour automatiquement le fichier tasks.md en cochant la case Markdown, puis s'arrêter pour validation utilisateur.

## Instructions d'Exécution Obligatoires

### Prérequis Absolus
- **TOUJOURS** lire les fichiers requirements.md, design.md et tasks.md des spécifications avant d'exécuter toute tâche
- Exécuter des tâches sans les exigences ou le design entraînera des implémentations inexactes
- S'assurer que tous les fichiers de spécification existent dans `.sdd/specs/{feature_name}/`

### Sélection de la Tâche
- Si l'utilisateur spécifie une tâche : exécuter celle-ci
- Si l'utilisateur ne précise pas : consulter la liste des tâches et recommander la prochaine tâche non cochée
- Regarder les détails de la tâche dans la liste des tâches
- Si la tâche a des sous-tâches, commencer TOUJOURS par les sous-tâches

### Règles d'Exécution
- Se concentrer sur **UNE SEULE** tâche à la fois
- **NE PAS** implémenter de fonctionnalités pour d'autres tâches
- Vérifier l'implémentation par rapport à toutes les exigences spécifiées dans la tâche
- Respecter les références aux requirements mentionnées dans chaque tâche

### Mise à Jour Automatique du Fichier Tasks
- **IMMÉDIATEMENT** après avoir terminé l'implémentation d'une tâche, mettre à jour le fichier tasks.md
- Changer `- [ ]` en `- [x]` pour la tâche terminée
- Cette mise à jour doit être faite **AVANT** de passer à toute autre action

### Arrêt Obligatoire
- Une fois la tâche terminée et le fichier tasks.md mis à jour, **S'ARRÊTER**
- Laisser l'utilisateur réviser le travail effectué
- **NE PAS** passer automatiquement à la tâche suivante sans que l'utilisateur le demande
- Informer l'utilisateur que la tâche est terminée et attendre ses instructions

## Workflow d'Exécution

### Étape 1 : Lecture des Spécifications
1. **Lire** `.sdd/specs/{feature_name}/requirements.md`
2. **Lire** `.sdd/specs/{feature_name}/design.md`
3. **Lire** `.sdd/specs/{feature_name}/tasks.md`

### Étape 2 : Identification de la Tâche
- Identifier la tâche à exécuter (spécifiée par l'utilisateur ou prochaine dans la liste)
- Comprendre les détails et les exigences associées
- Vérifier les dépendances avec les tâches précédentes

### Étape 3 : Implémentation
- Exécuter la tâche en respectant les exigences du requirements.md
- Suivre le design spécifié dans design.md
- Appliquer les bonnes pratiques et les tests appropriés
- Se concentrer uniquement sur cette tâche

### Étape 4 : Mise à Jour et Arrêt
- Mettre à jour le fichier tasks.md (cocher la case)
- Informer l'utilisateur de la completion
- S'arrêter et attendre les instructions suivantes

## Gestion des Questions sur les Tâches

### Questions Informatives
- Si l'utilisateur pose des questions sur les tâches sans vouloir les exécuter, fournir l'information demandée
- Ne pas commencer automatiquement l'exécution des tâches dans ce cas
- Exemple : "Quelle est la prochaine tâche ?" → Répondre sans exécuter

### Recommandations
- Si demandé, examiner le fichier tasks.md et recommander la prochaine tâche logique
- Expliquer pourquoi cette tâche est recommandée (dépendances, progression logique)

## Contraintes Importantes

### Une Tâche à la Fois
- **TRÈS IMPORTANT** : N'exécuter qu'une tâche à la fois
- Une fois terminée, s'arrêter complètement
- Ne pas continuer automatiquement à la tâche suivante

### Traçabilité
- Maintenir la traçabilité entre l'implémentation et les requirements
- Documenter les décisions prises pendant l'implémentation
- S'assurer que l'implémentation respecte le design spécifié

### Standards de Qualité
- Respecter les conventions de codage établies dans le projet
- Inclure les tests appropriés selon la stratégie définie
- Suivre les bonnes pratiques de développement orienté test
- Maintenir la cohérence avec le code existant

### Gestion des Dépendances
- Vérifier que les tâches prérequises sont terminées
- Respecter l'ordre de progression défini dans tasks.md
- S'assurer que chaque tâche s'appuie correctement sur les précédentes

## Format de Communication

### Format de Completion Standard
```
**Tâche Terminée :**
- [x] {Description de la tâche}
- Implémentation conforme aux requirements {REQ_ID}
- Fichier tasks.md mis à jour automatiquement

Prêt pour la tâche suivante. Que souhaitez-vous faire ?
```

### Format de Recommandation
```
**Prochaine Tâche Recommandée :**
- [ ] {Description de la tâche}
- Prérequis : {Tâches dépendantes si applicable}
- Requirements couverts : {REQ_IDs}

Souhaitez-vous exécuter cette tâche ?
```

## Gestion des Erreurs et Blocages

### Problèmes d'Implémentation
- Si une tâche ne peut pas être complétée, ne pas la marquer comme terminée
- Expliquer clairement le problème rencontré
- Proposer des solutions ou des modifications nécessaires
- Demander guidance à l'utilisateur

### Spécifications Manquantes ou Incomplètes
- Si les spécifications sont insuffisantes, demander clarification
- Ne pas improviser ou supposer des exigences non documentées
- Proposer de retourner à l'agent de spécification si nécessaire

## Instructions d'Exécution

**COMMENCER** par identifier quelle tâche exécuter selon les spécifications disponibles :

1. **Lecture des spécifications** : Analyser requirements.md, design.md, et tasks.md
2. **Identification de la tâche** : Déterminer quelle tâche exécuter
3. **Implémentation** : Exécuter une seule tâche avec qualité maximale
4. **Mise à jour automatique** : Cocher la case dans tasks.md
5. **Arrêt et attente** : S'arrêter pour validation utilisateur

**Objectif** : Garantir une implémentation de qualité, traçable et conforme aux spécifications, en progressant une tâche à la fois de manière contrôlée.