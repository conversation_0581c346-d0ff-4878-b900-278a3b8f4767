# ARCHITECTURE.md - Serveur MCP pour Workflow SDD

## Vue d'Ensemble

Cette architecture décrit l'implémentation d'un **serveur MCP (Model Context Protocol)** moderne et robuste pour le support d'un workflow de développement SDD (Spec Driven Development). L'architecture respecte les standards MCP officiels tout en intégrant les meilleures pratiques d'architecture distribuée pour la gestion d'état, la validation et l'orchestration de workflows complexes.

### Principes Architecturaux

- **Conformité MCP**: Respect strict du protocole JSON-RPC 2.0 et des spécifications MCP
- **Architecture Orchestrée**: Serveur unique avec modules spécialisés pour la simplité et la cohérence
- **Extensibilité**: Conception modulaire permettant l'ajout de nouvelles capacités
- **Robustesse**: Gestion d'état persistante avec versioning et rollback
- **Sécurité**: Authentification, autorisation et validation systématique
- **Observabilité**: Monitoring intégré et métriques business

## Architecture Technique

### Vue d'Ensemble du Système

```mermaid
graph TB
    subgraph "Clients MCP"
        CLAUDE[Claude Desktop]
        VSCODE[VS Code + MCP]
        CUSTOM[Clients Personnalisés]
    end
    
    subgraph "Serveur MCP SDD"
        subgraph "Couche Protocole"
            PROTO[Protocol Layer]
            CONN[Connection Manager]
            CAP[Capability Negotiation]
        end
        
        subgraph "Couche Orchestration"
            CORE[Core Engine]
            WM[Workflow Manager]
            ROUTER[Request Router]
        end
        
        subgraph "Couche Services"
            VALID[Validation Engine]
            STATE[State Manager]
            TEMPL[Template Engine]
            PROMPT[Prompt Orchestrator]
            EXEC[Execution Engine]
        end
        
        subgraph "Couche Infrastructure"
            CACHE[Redis Cache]
            DB[PostgreSQL DB]
            METRICS[Metrics Collector]
            LOGGER[Structured Logger]
        end
    end
    
    CLAUDE <-->|JSON-RPC 2.0| PROTO
    VSCODE <-->|JSON-RPC 2.0| PROTO
    CUSTOM <-->|JSON-RPC 2.0| PROTO
    
    PROTO <--> CONN
    CONN <--> CAP
    CAP <--> CORE
    
    CORE <--> WM
    CORE <--> ROUTER
    
    ROUTER <--> VALID
    ROUTER <--> STATE
    ROUTER <--> TEMPL
    ROUTER <--> PROMPT
    ROUTER <--> EXEC
    
    STATE <--> CACHE
    STATE <--> DB
    METRICS <--> LOGGER
```

### Couche Protocole MCP

#### Protocol Layer
**Responsabilité**: Gestion du protocole MCP standard
```typescript
interface ProtocolLayer {
  handleMessage(message: JsonRpcMessage): Promise<JsonRpcResponse>
  setupCapabilities(): ServerCapabilities
  validateProtocolVersion(version: string): boolean
  handleHeartbeat(): Promise<void>
}

class MCPProtocolHandler implements ProtocolLayer {
  private static readonly SUPPORTED_VERSIONS = ['2024-11-05', '2025-03-26']
  
  async handleMessage(message: JsonRpcMessage): Promise<JsonRpcResponse> {
    // Validation JSON-RPC 2.0
    this.validateJsonRpcFormat(message)
    
    // Routage vers les handlers appropriés
    const handler = this.getMethodHandler(message.method)
    const result = await handler.execute(message.params)
    
    return this.formatResponse(message.id, result)
  }
  
  setupCapabilities(): ServerCapabilities {
    return {
      resources: {
        subscribe: true,
        listChanged: true
      },
      tools: {
        listChanged: true
      },
      prompts: {
        listChanged: true
      },
      logging: {},
      sampling: {},
      experimental: {
        'sdd-workflow': {
          version: '1.0.0',
          features: ['validation', 'orchestration', 'templates']
        }
      }
    }
  }
}
```

#### Connection Manager
**Responsabilité**: Gestion des connexions et transports
```typescript
class ConnectionManager {
  private connections: Map<string, MCPConnection> = new Map()
  private transports: TransportProvider[]
  
  async initialize(): Promise<void> {
    // Support multi-transport (stdio, websocket, http)
    this.transports = [
      new StdioTransport(),
      new WebSocketTransport({ port: 3001 }),
      new HttpTransport({ port: 3000, path: '/mcp' })
    ]
    
    // Démarrage des transports
    await Promise.all(this.transports.map(t => t.start()))
    
    // Configuration du heartbeat
    this.setupHeartbeat()
  }
  
  private setupHeartbeat(): void {
    setInterval(async () => {
      for (const [id, connection] of this.connections) {
        try {
          await connection.ping()
        } catch (error) {
          await this.handleConnectionError(id, error)
        }
      }
    }, 30000) // 30 secondes
  }
}
```

### Couche Orchestration

#### Core Engine
**Responsabilité**: Point d'entrée central et orchestration
```typescript
class CoreEngine {
  constructor(
    private connectionManager: ConnectionManager,
    private workflowManager: WorkflowManager,
    private requestRouter: RequestRouter,
    private stateManager: StateManager
  ) {}
  
  async initialize(): Promise<void> {
    // Initialisation des composants dans l'ordre
    await this.stateManager.initialize()
    await this.connectionManager.initialize()
    await this.workflowManager.initialize()
    
    // Configuration des handlers MCP
    this.setupMCPHandlers()
    
    // Démarrage du monitoring
    this.startHealthMonitoring()
  }
  
  private setupMCPHandlers(): void {
    this.connectionManager.on('request', async (request) => {
      return this.requestRouter.route(request)
    })
    
    this.connectionManager.on('notification', async (notification) => {
      await this.handleNotification(notification)
    })
  }
}
```

#### Workflow Manager
**Responsabilité**: Orchestration du workflow SDD
```typescript
interface WorkflowState {
  projectId: string
  currentPhase: SDDPhase
  specifications: Map<string, SpecificationState>
  history: WorkflowEvent[]
}

enum SDDPhase {
  REQUIREMENTS = 'requirements',
  DESIGN = 'design', 
  TASKS = 'tasks',
  EXECUTION = 'execution',
  COMPLETED = 'completed'
}

class WorkflowManager {
  private workflows: Map<string, WorkflowState> = new Map()
  
  async transitionPhase(
    specId: string, 
    toPhase: SDDPhase,
    validationRequired: boolean = true
  ): Promise<TransitionResult> {
    const workflow = await this.getWorkflowForSpec(specId)
    const spec = workflow.specifications.get(specId)
    
    if (validationRequired) {
      const validationResult = await this.validatePhaseTransition(spec, toPhase)
      if (!validationResult.valid) {
        throw new WorkflowError('Phase transition validation failed', validationResult.issues)
      }
    }
    
    // Mise à jour atomique de l'état
    await this.updateSpecState(specId, {
      currentPhase: toPhase,
      transitionedAt: new Date(),
      previousPhase: spec.currentPhase
    })
    
    // Notification des clients abonnés
    await this.notifyPhaseTransition(specId, spec.currentPhase, toPhase)
    
    return { success: true, newPhase: toPhase }
  }
}
```

### Couche Services

#### Validation Engine
**Responsabilité**: Validation multi-règles et multi-phases
```typescript
interface ValidationRule {
  id: string
  name: string
  description: string
  phase: SDDPhase
  severity: 'error' | 'warning' | 'info'
  validate(content: string, context: ValidationContext): Promise<ValidationResult>
}

class ValidationEngine {
  private rules: Map<SDDPhase, ValidationRule[]> = new Map()
  private customValidators: Map<string, CustomValidator> = new Map()
  
  async validateContent(
    phase: SDDPhase,
    content: string,
    context: ValidationContext
  ): Promise<ValidationReport> {
    const rules = this.rules.get(phase) || []
    
    // Validation parallèle pour performance
    const validationPromises = rules.map(async rule => {
      try {
        return await rule.validate(content, context)
      } catch (error) {
        return {
          ruleId: rule.id,
          status: 'error',
          message: `Rule execution failed: ${error.message}`,
          severity: 'error'
        }
      }
    })
    
    const results = await Promise.all(validationPromises)
    
    return this.generateValidationReport(results, context)
  }
  
  registerRule(phase: SDDPhase, rule: ValidationRule): void {
    if (!this.rules.has(phase)) {
      this.rules.set(phase, [])
    }
    this.rules.get(phase)!.push(rule)
  }
}

// Exemple de règle de validation
class UserStoryFormatRule implements ValidationRule {
  id = 'req-user-story-format'
  name = 'User Story Format Validation'
  description = 'Vérifie le format standard des user stories'
  phase = SDDPhase.REQUIREMENTS
  severity = 'error' as const
  
  async validate(content: string, context: ValidationContext): Promise<ValidationResult> {
    const userStoryPattern = /En tant que .+?, je veux .+?, afin de .+?[.]/g
    const lines = content.split('\n')
    const issues: ValidationIssue[] = []
    
    lines.forEach((line, index) => {
      if (line.includes('En tant que') && !userStoryPattern.test(line)) {
        issues.push({
          line: index + 1,
          column: 1,
          message: 'Format de user story incorrect. Utilisez: "En tant que [rôle], je veux [fonctionnalité], afin de [bénéfice]."',
          severity: this.severity,
          ruleId: this.id
        })
      }
    })
    
    return {
      ruleId: this.id,
      status: issues.length > 0 ? 'failed' : 'passed',
      issues,
      executionTime: Date.now() - context.startTime
    }
  }
}
```

#### State Manager
**Responsabilité**: Persistence et gestion d'état
```typescript
interface ProjectState {
  id: string
  name: string
  description: string
  techStack: string[]
  createdAt: Date
  updatedAt: Date
  specifications: Map<string, SpecificationState>
  metadata: ProjectMetadata
}

class StateManager {
  constructor(
    private database: DatabaseConnection,
    private cache: CacheInterface,
    private eventEmitter: EventEmitter
  ) {}
  
  async saveProjectState(projectId: string, state: ProjectState): Promise<void> {
    const transaction = await this.database.beginTransaction()
    
    try {
      // Sauvegarde atomique avec versioning
      await this.saveProjectMetadata(transaction, state)
      await this.saveSpecifications(transaction, projectId, state.specifications)
      await this.createStateVersion(transaction, projectId, state)
      
      await transaction.commit()
      
      // Mise à jour cache après commit DB
      await this.cache.set(`project:${projectId}`, state, 3600)
      
      // Émission d'événement pour notifications
      this.eventEmitter.emit('project:state:changed', {
        projectId,
        state,
        timestamp: new Date()
      })
      
    } catch (error) {
      await transaction.rollback()
      throw new StateManagerError(`Failed to save project state: ${error.message}`)
    }
  }
  
  async getProjectState(projectId: string): Promise<ProjectState | null> {
    // Tentative cache d'abord
    const cached = await this.cache.get(`project:${projectId}`)
    if (cached) return cached
    
    // Reconstruction depuis DB avec optimisations
    const state = await this.reconstructStateFromDB(projectId)
    if (state) {
      await this.cache.set(`project:${projectId}`, state, 3600)
    }
    
    return state
  }
  
  async rollbackToVersion(projectId: string, version: number): Promise<void> {
    const versionData = await this.getStateVersion(projectId, version)
    if (!versionData) {
      throw new StateManagerError(`Version ${version} not found for project ${projectId}`)
    }
    
    await this.saveProjectState(projectId, versionData.state)
  }
}
```

#### Template Engine
**Responsabilité**: Génération de contenu dynamique
```typescript
interface TemplateContext {
  project: ProjectMetadata
  phase: SDDPhase
  userInput?: string
  constraints?: string[]
  examples?: string[]
}

class TemplateEngine {
  private templates: Map<string, CompiledTemplate> = new Map()
  private helpers: Map<string, TemplateHelper> = new Map()
  
  async renderTemplate(templateId: string, context: TemplateContext): Promise<string> {
    const template = await this.getTemplate(templateId)
    
    const enrichedContext = {
      ...context,
      helpers: this.helpers,
      formatters: this.getFormatters(),
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }
    
    return template.render(enrichedContext)
  }
  
  private getFormatters() {
    return {
      userStory: (role: string, want: string, benefit: string) =>
        `En tant que ${role}, je veux ${want}, afin de ${benefit}.`,
      
      acceptanceCriteria: (condition: string, action: string) =>
        `QUAND ${condition} ALORS ${action}`,
        
      taskId: (baseId: string, index: number) =>
        `${baseId}.${index.toString().padStart(2, '0')}`
    }
  }
}
```

#### Prompt Orchestrator
**Responsabilité**: Génération de prompts contextuels
```typescript
class PromptOrchestrator {
  constructor(
    private templateEngine: TemplateEngine,
    private stateManager: StateManager
  ) {}
  
  async generatePrompt(
    phase: SDDPhase,
    specId: string,
    userInput?: string
  ): Promise<GeneratedPrompt> {
    const projectState = await this.getProjectStateForSpec(specId)
    const context = await this.buildPhaseContext(phase, projectState, userInput)
    
    const templateId = this.selectPromptTemplate(phase, context)
    const promptContent = await this.templateEngine.renderTemplate(templateId, context)
    
    return {
      content: promptContent,
      metadata: {
        phase,
        specId,
        templateId,
        contextSize: this.calculateContextSize(context),
        generatedAt: new Date().toISOString()
      }
    }
  }
  
  private async buildPhaseContext(
    phase: SDDPhase,
    projectState: ProjectState,
    userInput?: string
  ): Promise<TemplateContext> {
    const baseContext = {
      project: {
        name: projectState.name,
        description: projectState.description,
        techStack: projectState.techStack
      },
      phase,
      userInput: userInput || ''
    }
    
    switch (phase) {
      case SDDPhase.REQUIREMENTS:
        return {
          ...baseContext,
          template: await this.templateEngine.renderTemplate('requirements-template', baseContext),
          guidelines: await this.getValidationGuidelines('requirements'),
          examples: await this.getPhaseExamples('requirements')
        }
        
      case SDDPhase.DESIGN:
        const requirements = await this.getValidatedRequirements(projectState)
        return {
          ...baseContext,
          requirementsSummary: requirements,
          constraints: await this.getTechnicalConstraints(projectState),
          existingComponents: await this.getExistingComponents(projectState)
        }
        
      default:
        return baseContext
    }
  }
}
```

## Outils MCP Exposés

### Outils de Gestion de Projet

#### sdd_project_init
**Fonction**: Initialise un nouveau projet SDD
**Schéma**:
```json
{
  "name": "sdd_project_init",
  "description": "Initialise un nouveau projet SDD avec la structure complète",
  "inputSchema": {
    "type": "object",
    "properties": {
      "project_name": {
        "type": "string",
        "description": "Nom du projet (kebab-case)",
        "pattern": "^[a-z0-9-]+$",
        "minLength": 3,
        "maxLength": 50
      },
      "description": {
        "type": "string",
        "description": "Description détaillée du projet",
        "minLength": 10,
        "maxLength": 500
      },
      "tech_stack": {
        "type": "array",
        "description": "Technologies utilisées",
        "items": {
          "type": "string",
          "enum": ["typescript", "react", "node", "python", "java", "go", "rust"]
        }
      },
      "project_type": {
        "type": "string",
        "enum": ["web_app", "mobile_app", "api", "cli_tool", "library"]
      }
    },
    "required": ["project_name", "description", "tech_stack"]
  }
}
```

#### sdd_project_status
**Fonction**: Affiche l'état détaillé du projet
**Fonctionnalités**:
- Vue d'ensemble du projet et métriques
- État de toutes les spécifications
- Progression globale et blocages
- Historique des modifications récentes
- Recommandations d'actions

### Outils de Workflow

#### sdd_spec_create
**Fonction**: Démarre une nouvelle spécification
**Processus**:
1. Validation des prérequis projet
2. Création de la structure de spécification
3. Génération du prompt contextualisé
4. Initialisation du suivi de progression

#### sdd_phase_advance
**Fonction**: Fait progresser une spécification vers la phase suivante
**Logique**:
```typescript
async function advancePhase(specId: string, options: AdvanceOptions): Promise<AdvanceResult> {
  const currentState = await stateManager.getSpecState(specId)
  const nextPhase = getNextPhase(currentState.currentPhase)
  
  if (options.validationRequired) {
    const validation = await validationEngine.validatePhase(specId, currentState.currentPhase)
    if (!validation.passed) {
      throw new ValidationError('Cannot advance: validation failed', validation.issues)
    }
  }
  
  await workflowManager.transitionPhase(specId, nextPhase)
  
  return {
    success: true,
    newPhase: nextPhase,
    nextActions: getNextActions(nextPhase)
  }
}
```

### Outils de Validation

#### sdd_validate_requirements
**Fonction**: Validation complète des exigences
**Règles appliquées**:
- Format des user stories (En tant que... je veux... afin de...)
- Présence des critères d'acceptation EARS
- Cohérence avec la vision produit
- Complétude des exigences non fonctionnelles
- Standards de sécurité

#### sdd_validate_design  
**Fonction**: Validation de la conception technique
**Aspects validés**:
- Alignement avec les exigences
- Cohérence architecturale
- Respect des contraintes techniques
- Complétude des diagrammes
- Stratégie de tests définie

#### sdd_validate_tasks
**Fonction**: Validation de la décomposition en tâches
**Critères**:
- Atomicité des tâches
- Faisabilité individuelle
- Respect des dépendances
- Estimation cohérente
- Complétude de la couverture

### Outils d'Exécution

#### sdd_execute_task
**Fonction**: Exécution guidée d'une tâche
**Fonctionnalités avancées**:
- Contexte d'exécution enrichi
- Monitoring en temps réel
- Validation de la qualité du résultat
- Intégration continue (tests, build)
- Commit automatique avec messages structurés

## Ressources MCP Exposées

### Templates SDD
```
URI: sdd://templates/{template_name}
Types: requirements-template.md, design-template.md, tasks-template.md
```

### État des Projets
```
URI: sdd://projects/{project_id}/state
Format: JSON avec état complet du projet
```

### Métriques et Analytics
```
URI: sdd://analytics/{project_id}?period={period}&metrics={metrics}
Types: progression, qualité, performance, prédictions
```

## Prompts MCP Structurés

### Prompt Requirements Generation
**Usage**: Génération assistée d'exigences
**Paramètres**: contexte projet, description fonctionnalité, utilisateurs cibles
**Sortie**: Prompt contextualisé avec guidelines et exemples

### Prompt Design Creation  
**Usage**: Création de conception technique
**Paramètres**: exigences validées, contraintes techniques, architecture existante
**Sortie**: Prompt technique avec patterns et bonnes pratiques

### Prompt Task Decomposition
**Usage**: Décomposition en tâches atomiques
**Paramètres**: conception validée, contraintes d'implémentation
**Sortie**: Guide de décomposition avec templates

## Gestion d'État Avancée

### Modèle de Données

#### Tables Principales
```sql
-- Projets
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    tech_stack JSONB,
    project_type project_type_enum DEFAULT 'web_app',
    config JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Spécifications
CREATE TABLE specifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    feature_name VARCHAR(255) NOT NULL,
    current_phase spec_phase_enum DEFAULT 'requirements',
    status spec_status_enum DEFAULT 'in_progress',
    priority priority_enum DEFAULT 'medium',
    complexity complexity_enum DEFAULT 'medium',
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Phases de spécification avec versioning
CREATE TABLE spec_phases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    spec_id UUID REFERENCES specifications(id) ON DELETE CASCADE,
    phase_type spec_phase_enum NOT NULL,
    content TEXT,
    validation_status validation_status_enum DEFAULT 'pending',
    validation_report JSONB,
    version INTEGER DEFAULT 1,
    hash VARCHAR(64),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(spec_id, phase_type, version)
);

-- Tâches avec dépendances
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    spec_id UUID REFERENCES specifications(id) ON DELETE CASCADE,
    task_number VARCHAR(50) NOT NULL,
    parent_task_id UUID REFERENCES tasks(id),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    status task_status_enum DEFAULT 'pending',
    priority priority_enum DEFAULT 'medium',
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2),
    assignee VARCHAR(255),
    dependencies UUID[],
    files_affected TEXT[],
    execution_log JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- Historique des événements
CREATE TABLE workflow_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    spec_id UUID REFERENCES specifications(id) ON DELETE CASCADE,
    event_type event_type_enum NOT NULL,
    actor VARCHAR(255),
    data JSONB,
    timestamp TIMESTAMP DEFAULT NOW(),
    
    INDEX idx_workflow_events_project_time (project_id, timestamp DESC),
    INDEX idx_workflow_events_spec_time (spec_id, timestamp DESC)
);

-- Types énumérés
CREATE TYPE spec_phase_enum AS ENUM ('requirements', 'design', 'tasks', 'execution', 'completed');
CREATE TYPE spec_status_enum AS ENUM ('in_progress', 'validation_pending', 'validated', 'rejected', 'blocked');
CREATE TYPE validation_status_enum AS ENUM ('pending', 'validated', 'rejected', 'warning');
CREATE TYPE task_status_enum AS ENUM ('pending', 'in_progress', 'completed', 'failed', 'skipped', 'blocked');
CREATE TYPE priority_enum AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE complexity_enum AS ENUM ('simple', 'medium', 'complex', 'epic');
CREATE TYPE project_type_enum AS ENUM ('web_app', 'mobile_app', 'api', 'cli_tool', 'library', 'desktop_app');
CREATE TYPE event_type_enum AS ENUM ('project_created', 'spec_created', 'phase_transitioned', 'validation_completed', 'task_executed', 'error_occurred');
```

### Cache Strategy

#### Cache Multi-Niveaux
```typescript
class CacheStrategy {
  private l1Cache: Map<string, any> = new Map() // Memory cache
  private l2Cache: RedisClient // Redis cache
  private l3Cache: DatabaseConnection // DB cache via materialized views
  
  async get(key: string): Promise<any> {
    // L1: Memory (ultra-rapide)
    let value = this.l1Cache.get(key)
    if (value) return value
    
    // L2: Redis (rapide)
    value = await this.l2Cache.get(key)
    if (value) {
      this.l1Cache.set(key, value)
      return value
    }
    
    // L3: DB avec vues matérialisées (plus lent mais complet)
    value = await this.loadFromDatabase(key)
    if (value) {
      await this.l2Cache.setex(key, 3600, JSON.stringify(value))
      this.l1Cache.set(key, value)
    }
    
    return value
  }
}
```

## Sécurité et Monitoring

### Authentification et Autorisation

#### Modèle de Sécurité
```typescript
interface SecurityContext {
  userId: string
  sessionId: string
  permissions: Permission[]
  clientId: string
  ipAddress: string
  userAgent: string
}

class SecurityManager {
  async authenticateConnection(credentials: ConnectionCredentials): Promise<SecurityContext> {
    // Validation JWT
    const token = await this.validateJWT(credentials.token)
    
    // Récupération des permissions
    const permissions = await this.getPermissions(token.userId)
    
    // Vérification des quotas
    await this.checkRateLimits(token.userId, credentials.clientId)
    
    // Audit de connexion
    await this.auditLogger.logAuth({
      userId: token.userId,
      clientId: credentials.clientId,
      status: 'success',
      ipAddress: credentials.ipAddress
    })
    
    return {
      userId: token.userId,
      sessionId: generateSessionId(),
      permissions,
      clientId: credentials.clientId,
      ipAddress: credentials.ipAddress,
      userAgent: credentials.userAgent
    }
  }
  
  async authorizeOperation(context: SecurityContext, operation: string, resource: string): Promise<boolean> {
    const required = this.getRequiredPermission(operation, resource)
    return context.permissions.some(p => p.covers(required))
  }
}
```

#### Rate Limiting
```typescript
class RateLimiter {
  private limits = {
    'tools/call': { requests: 60, window: 60000 }, // 60 req/min
    'resources/read': { requests: 120, window: 60000 }, // 120 req/min
    'prompts/get': { requests: 30, window: 60000 } // 30 req/min
  }
  
  async checkLimit(userId: string, operation: string): Promise<boolean> {
    const limit = this.limits[operation] || { requests: 30, window: 60000 }
    const key = `rate_limit:${userId}:${operation}`
    
    const current = await this.redis.get(key) || 0
    if (current >= limit.requests) {
      throw new RateLimitError(`Rate limit exceeded: ${operation}`)
    }
    
    await this.redis.setex(key, limit.window / 1000, current + 1)
    return true
  }
}
```

### Observabilité

#### Métriques Business
```typescript
class BusinessMetrics {
  private metrics = {
    // Métriques de workflow
    'sdd.projects.created': new Counter(),
    'sdd.specs.created': new Counter(),
    'sdd.phases.completed': new Counter(),
    'sdd.validations.success_rate': new Gauge(),
    'sdd.tasks.execution_time': new Histogram(),
    
    // Métriques de qualité
    'sdd.validation.errors_per_phase': new Histogram(),
    'sdd.requirements.user_stories_count': new Gauge(),
    'sdd.design.complexity_score': new Gauge(),
    'sdd.tasks.completion_rate': new Gauge(),
    
    // Métriques techniques
    'mcp.connections.active': new Gauge(),
    'mcp.requests.total': new Counter(),
    'mcp.requests.duration': new Histogram(),
    'mcp.cache.hit_rate': new Gauge()
  }
  
  recordWorkflowEvent(event: WorkflowEvent): void {
    switch (event.type) {
      case 'project_created':
        this.metrics['sdd.projects.created'].inc()
        break
        
      case 'phase_transitioned':
        this.metrics['sdd.phases.completed'].inc()
        this.metrics['sdd.phases.duration'].observe(event.duration)
        break
        
      case 'validation_completed':
        this.metrics['sdd.validations.success_rate'].set(event.successRate)
        break
    }
  }
}
```

#### Monitoring Santé Système
```typescript
class HealthMonitor {
  async checkHealth(): Promise<HealthReport> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkMemoryUsage(),
      this.checkDiskSpace(),
      this.checkActiveConnections()
    ])
    
    const results = checks.map((check, index) => ({
      name: ['database', 'redis', 'memory', 'disk', 'connections'][index],
      status: check.status === 'fulfilled' ? 'healthy' : 'unhealthy',
      details: check.status === 'fulfilled' ? check.value : check.reason
    }))
    
    const overallStatus = results.every(r => r.status === 'healthy') ? 'healthy' : 'degraded'
    
    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks: results,
      uptime: process.uptime(),
      version: '1.0.0'
    }
  }
}
```

## Déploiement et Configuration

### Configuration Docker

#### Dockerfile Multi-Stage
```dockerfile
# Stage 1: Build
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Stage 2: Runtime
FROM node:18-alpine AS runtime

# Sécurité: utilisateur non-root
RUN addgroup -g 1001 -S sddmcp && \
    adduser -S sddmcp -u 1001

# Installation des dépendances système
RUN apk add --no-cache \
    postgresql-client \
    redis-tools \
    curl \
    dumb-init

WORKDIR /app

# Copie des dépendances et du code
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=sddmcp:sddmcp . .

USER sddmcp

EXPOSE 3000 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Utilisation de dumb-init pour la gestion des signaux
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]
```

#### Docker Compose Production
```yaml
version: '3.8'

services:
  sdd-mcp-server:
    build: .
    ports:
      - "3000:3000"    # HTTP/WebSocket
      - "3001:3001"    # MCP WebSocket
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: sdd_mcp
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      LOG_LEVEL: info
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./config:/app/config:ro
      - ./templates:/app/templates:ro
      - logs:/app/logs
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
    restart: unless-stopped
    
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: sdd_mcp
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d sdd_mcp"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
    restart: unless-stopped
    
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
    restart: unless-stopped
    
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3003:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_INSTALL_PLUGINS: redis-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  logs:

networks:
  default:
    name: sdd-mcp-network
```

### Configuration Avancée

#### Configuration Serveur
```yaml
# config/production.yaml
server:
  name: "SDD-MCP-Server"
  version: "1.0.0"
  host: "0.0.0.0"
  port: 3000
  
mcp:
  protocol_version: "2024-11-05"
  transports:
    stdio:
      enabled: true
    websocket:
      enabled: true
      port: 3001
      cors:
        origins: ["*"]
        credentials: true
    http:
      enabled: true
      path: "/mcp"
      cors:
        origins: ["https://claude.ai", "https://cursor.sh"]
        
database:
  host: "${DB_HOST}"
  port: 5432
  database: "${DB_NAME}"
  username: "${DB_USER}"
  password: "${DB_PASSWORD}"
  pool:
    min: 5
    max: 20
    acquireTimeoutMillis: 30000
    idleTimeoutMillis: 600000
  ssl:
    enabled: true
    rejectUnauthorized: false
    
cache:
  redis:
    host: "${REDIS_HOST}"
    port: 6379
    password: "${REDIS_PASSWORD}"
    db: 0
  strategy:
    l1_max_size: 1000
    l2_ttl: 3600
    l3_refresh_interval: 300
    
security:
  jwt:
    secret: "${JWT_SECRET}"
    expiresIn: "24h"
    algorithm: "HS256"
  rate_limiting:
    window_ms: 60000
    max_requests: 60
    skip_successful: false
  cors:
    origins: ["*"]
    methods: ["GET", "POST"]
    allowed_headers: ["Content-Type", "Authorization"]
    
logging:
  level: "info"
  format: "json"
  outputs:
    - type: "console"
    - type: "file"
      path: "logs/sdd-mcp.log"
      maxSize: "100MB"
      maxFiles: 10
      
monitoring:
  metrics:
    enabled: true
    port: 9090
    path: "/metrics"
  health:
    enabled: true
    path: "/health"
  tracing:
    enabled: true
    jaeger_endpoint: "${JAEGER_ENDPOINT}"
    
workflow:
  validation:
    timeout_ms: 30000
    max_parallel: 5
    auto_fix: false
  execution:
    timeout_ms: 300000
    max_parallel: 3
    retry_attempts: 2
  templates:
    path: "templates"
    cache_enabled: true
    watch_changes: false
```

## Plan d'Implémentation

### Phase 1: Foundation MCP (6-8 semaines)

#### Semaines 1-2: Core Protocol
- [ ] Implémentation JSON-RPC 2.0 complet
- [ ] Gestionnaire de connexions multi-transport
- [ ] Négociation des capacités MCP
- [ ] Tests protocole avec MCP Inspector

#### Semaines 3-4: State Management
- [ ] Modèle de données PostgreSQL
- [ ] Système de cache Redis multicouches  
- [ ] Versioning et rollback
- [ ] Migrations et seeding

#### Semaines 5-6: Security & Infrastructure
- [ ] Authentification JWT
- [ ] Rate limiting et autorisation
- [ ] Logging structuré
- [ ] Monitoring de base

#### Semaines 7-8: Templates & Validation
- [ ] Template Engine avec Handlebars
- [ ] Moteur de validation extensible
- [ ] Règles de validation SDD
- [ ] Tests d'intégration

### Phase 2: Workflow SDD (8-10 semaines)

#### Semaines 9-10: Workflow Manager
- [ ] Machine à états des phases
- [ ] Transitions et validations
- [ ] Gestion des dépendances
- [ ] Événements et notifications

#### Semaines 11-12: Outils Projet
- [ ] sdd_project_init complet
- [ ] sdd_project_status avec métriques
- [ ] Configuration et templates
- [ ] Tests end-to-end

#### Semaines 13-14: Outils Spécifications
- [ ] sdd_spec_create avec contexte
- [ ] sdd_phase_advance avec validation
- [ ] Prompts génératifs
- [ ] Intégration workflow

#### Semaines 15-16: Validation Multi-Phase
- [ ] sdd_validate_requirements
- [ ] sdd_validate_design  
- [ ] sdd_validate_tasks
- [ ] Rapports détaillés

#### Semaines 17-18: Exécution Avancée
- [ ] sdd_execute_task avec monitoring
- [ ] Intégration outils développement
- [ ] Commits automatiques
- [ ] Métriques qualité

### Phase 3: Optimisation & Production (6-8 semaines)

#### Semaines 19-20: Performance
- [ ] Optimisation requêtes DB
- [ ] Cache intelligent
- [ ] Compression réponses
- [ ] Load testing

#### Semaines 21-22: Observabilité
- [ ] Métriques business Prometheus
- [ ] Dashboards Grafana
- [ ] Alerting intelligent  
- [ ] Tracing distribué

#### Semaines 23-24: Déploiement
- [ ] Configuration Docker production
- [ ] CI/CD avec tests
- [ ] Infrastructure as Code
- [ ] Monitoring production

#### Semaines 25-26: Documentation & Support
- [ ] Documentation API complète
- [ ] Guides utilisateur
- [ ] Exemples intégration
- [ ] Support communauté

### Phase 4: Extensions & Intégrations (4-6 semaines)

#### Semaines 27-28: Intégrations IDE
- [ ] Extension VS Code
- [ ] Plugin Cursor
- [ ] Configuration Claude Desktop
- [ ] Tests utilisateurs

#### Semaines 29-30: Features Avancées
- [ ] IA-assisted validation
- [ ] Prédictions de complexité
- [ ] Recommandations automatiques
- [ ] Analytics avancés

## Métriques de Succès

### Métriques Techniques
- **Performance**: < 500ms temps de réponse p95
- **Disponibilité**: > 99.5% uptime
- **Fiabilité**: < 0.1% taux d'erreur
- **Scalabilité**: Support 100+ connexions simultanées

### Métriques Business
- **Adoption**: 50+ projets créés dans les 3 premiers mois
- **Engagement**: 80% des projets complètent au moins une spécification
- **Qualité**: 90% des validations passent après corrections
- **Productivité**: 30% de réduction du temps de spécification

### Métriques Qualité
- **Couverture tests**: > 90%
- **Documentation**: 100% des APIs documentées
- **Sécurité**: Zéro vulnérabilité critique
- **Maintenabilité**: Score A+ SonarQube

---

Cette architecture fournit une base solide et extensible pour un serveur MCP SDD de niveau production, alliant les meilleures pratiques du protocole MCP avec les exigences spécifiques du workflow de développement dirigé par les spécifications.