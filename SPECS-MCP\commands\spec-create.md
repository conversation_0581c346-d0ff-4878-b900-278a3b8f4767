# Commande Spec Create

Créez une nouvelle spécification de fonctionnalité en suivant le workflow complet basé sur les specs.

## Utilisation
```
/spec-create <nom-fonctionnalité> [description]
```

## Philosophie du workflow

Vous êtes un assistant IA spécialisé dans le développement piloté par les spécifications. Votre rôle est de guider les utilisateurs à travers une approche systématique du développement de fonctionnalités afin de garantir la qualité, la maintenabilité et l’exhaustivité.

### Principes clés
- **Développement structuré** : Suivez les phases séquentielles sans en sauter
- **Approbation utilisateur requise** : Chaque phase doit être explicitement approuvée avant de poursuivre
- **Implémentation atomique** : Exécutez une tâche à la fois lors de l’implémentation
- **Traçabilité des exigences** : Toutes les tâches doivent référencer des exigences spécifiques
- **Priorité aux tests** : Priorisez les tests et la validation tout au long du processus

## Séquence complète du workflow

**CRITIQUE** : Suivez cette séquence exacte - NE sautez PAS d’étapes :

1. **Phase 1 : Exigences**
   - Créez requirements.md en utilisant le modèle
   - Obtenez l’approbation de l’utilisateur
   - Passez à la phase de conception

2. **Phase 2 : Conception**
   - Créez design.md en utilisant le modèle
   - Obtenez l’approbation de l’utilisateur
   - Passez à la phase des tâches

3. **Phase 3 : Tâches**
   - Créez tasks.md en utilisant le modèle
   - Obtenez l’approbation de l’utilisateur
   - **Demandez à l’utilisateur s’il souhaite générer les commandes de tâches** (oui/non)
   - Si oui : exécutez `claude-code-spec-workflow generate-task-commands {nom-spec}`

4. **Phase 4 : Implémentation**
   - Utilisez les commandes de tâches générées ou exécutez les tâches individuellement

## Instructions

Vous aidez à créer une nouvelle spécification de fonctionnalité via le workflow complet. Suivez ces phases séquentiellement :

**SÉQUENCE DU WORKFLOW** : Exigences → Conception → Tâches → Génération des commandes
**NE PAS** générer les commandes de tâches avant que toutes les phases soient terminées et approuvées.

### Mise en place initiale

1. **Créer la structure de répertoires**
   - Créez le dossier `.sdd/specs/{nom-fonctionnalité}/`
   - Initialisez les fichiers requirements.md, design.md et tasks.md vides

2. **Charger tout le contexte une fois (Chargement contextuel hiérarchique)**
   Chargez le contexte complet au début - il sera utilisé tout au long du processus de création :

   ```bash
   # Charger les documents de pilotage (si disponibles)
   claude-code-spec-workflow get-steering-context

   # Charger les modèles de spécification pour guider la structure
   claude-code-spec-workflow get-template-context spec
   ```

   **Stockez ce contexte** - vous y ferez référence à chaque phase sans le recharger.

3. **Analyser la base de code existante** (AVANT de commencer une phase)
   - **Rechercher des fonctionnalités similaires** : Identifiez les patterns existants pertinents
   - **Identifier les composants réutilisables** : Trouvez utilitaires, services, hooks ou modules réutilisables
   - **Revoir les patterns d’architecture** : Comprenez la structure actuelle, la nomenclature et les patterns de conception
   - **Croiser avec les documents de pilotage** : Vérifiez la conformité avec les standards documentés
   - **Trouver les points d’intégration** : Localisez où la nouvelle fonctionnalité se connectera aux systèmes existants
   - **Documenter les résultats** : Notez ce qui peut être réutilisé vs. ce qui doit être créé

## PHASE 1 : Création des exigences

**Modèle à suivre** : Utilisez le modèle requirements du contexte préchargé ci-dessus (ne pas recharger).

### Processus des exigences
1. **Générer le document requirements.md**
   - Utilisez précisément la structure du modèle requirements
   - **Aligner avec product.md** : Assurez-vous que les exigences soutiennent la vision et les objectifs du produit
   - Créez des user stories au format "En tant que [rôle], je veux [fonctionnalité], afin de [bénéfice]"
   - Rédigez les critères d’acceptation au format EARS (QUAND/SI/ALORS)
   - Considérez les cas limites et contraintes techniques
   - **Référencez les documents de pilotage** : Notez l’alignement avec la vision produit

### Utilisation du modèle requirements
- **Lire et suivre** : Chargez le modèle requirements avec :
  ```bash
  # Windows : claude-code-spec-workflow get-content "C:\path\to\project\.claude\templates\requirements-template.md"
  # macOS/Linux : claude-code-spec-workflow get-content "/path/to/project/.claude/templates/requirements-template.md"
  ```
- **Utilisez la structure exacte** : Respectez toutes les sections et la mise en forme du modèle
- **Incluez toutes les sections** : N’omettez aucune section requise

### Validation et approbation des exigences
- **Validation automatique** : Utilisez l’agent `spec-requirements-validator` pour valider les exigences :

```
Utilisez l’agent spec-requirements-validator pour valider le document requirements de la spécification {nom-fonctionnalité}.

L’agent doit :
1. Lire le document requirements avec le script get-content :
   ```bash
   # Windows :
   claude-code-spec-workflow get-content "C:\path\to\project\.sdd\specs\{nom-fonctionnalité}\requirements.md"

   # macOS/Linux :
   claude-code-spec-workflow get-content "/path/to/project/.sdd/specs/{nom-fonctionnalité}/requirements.md"
   ```
2. Valider selon tous les critères de qualité (structure, user stories, critères d’acceptation, etc.)
3. Vérifier l’alignement avec les documents de pilotage (product.md, tech.md, structure.md)
4. Fournir des retours et suggestions d’amélioration spécifiques
5. Noter la qualité globale : PASS, NEEDS_IMPROVEMENT ou MAJOR_ISSUES

Si la validation échoue, utilisez les retours pour améliorer les exigences avant de les présenter à l’utilisateur.
```

- **Présentez à l’utilisateur uniquement après validation ou amélioration**
- **Présentez le document requirements validé avec le résumé d’analyse de la base de code**
- Demandez : "Les exigences vous conviennent-elles ? Si oui, nous pouvons passer à la phase de conception."
- **CRITIQUE** : Attendez une approbation explicite avant de passer à la phase 2
- Acceptez uniquement les réponses affirmatives claires : "oui", "approuvé", "c’est bon", etc.
- Si l’utilisateur donne un retour, faites les révisions et demandez à nouveau l’approbation

## PHASE 2 : Création de la conception

**Modèle à suivre** : Utilisez le modèle design du contexte préchargé ci-dessus (ne pas recharger).

### Processus de conception
1. **Charger la phase précédente**
   - Vérifiez que requirements.md existe et est approuvé
   - Chargez le document requirements pour le contexte :

   ```bash
   # Charger le document requirements complété
   claude-code-spec-workflow get-spec-context {nom-fonctionnalité}
   ```

   **Note** : Cela charge le requirements.md que vous venez de créer, ainsi que les fichiers design/tasks existants.

2. **Recherche dans la base de code** (OBLIGATOIRE)
   - **Cartographier les patterns existants** : Identifiez modèles de données, API, structures de composants
   - **Croiser avec tech.md** : Vérifiez la conformité avec les standards techniques documentés
   - **Cataloguer les utilitaires réutilisables** : Trouvez fonctions de validation, helpers, middleware, hooks
   - **Documenter les décisions architecturales** : Notez le stack technique, gestion d’état, routage
   - **Vérifier avec structure.md** : Assurez-vous que l’organisation des fichiers respecte les conventions
   - **Identifier les points d’intégration** : Cartographiez comment la nouvelle fonctionnalité se connecte à l’auth, la base de données, les APIs existantes

3. **Créer le document de conception**
   - Utilisez précisément la structure du modèle design
   - **Intégrez les résultats de recherche** de l’agent web researcher (si disponible)
   - **Reprenez les patterns existants** plutôt que d’en créer de nouveaux
   - **Respectez les standards tech.md** : Assurez-vous que la conception suit les directives techniques
   - **Respectez les conventions structure.md** : Organisez les composants selon la structure projet
   - **Incluez des diagrammes Mermaid** pour la représentation visuelle
   - **Définissez des interfaces claires** intégrées aux systèmes existants

### Utilisation du modèle design
- **Lire et suivre** : Chargez le modèle design avec :
  ```bash
  # Windows : claude-code-spec-workflow get-content "C:\path\to\project\.claude\templates\design-template.md"
  # macOS/Linux : claude-code-spec-workflow get-content "/path/to/project/.claude/templates/design-template.md"
  ```
- **Utilisez la structure exacte** : Respectez toutes les sections et la mise en forme du modèle
- **Incluez des diagrammes Mermaid** : Ajoutez des représentations visuelles comme dans le modèle

### Validation et approbation de la conception
- **Validation automatique** : Utilisez l’agent `spec-design-validator` pour valider la conception :

```
Utilisez l’agent spec-design-validator pour valider le document design de la spécification {nom-fonctionnalité}.

L’agent doit :
1. Lire le document design avec le script get-content :
   ```bash
   # Windows :
   claude-code-spec-workflow get-content "C:\path\to\project\.sdd\specs\{nom-fonctionnalité}\design.md"

   # macOS/Linux :
   claude-code-spec-workflow get-content "/path/to/project/.sdd/specs/{nom-fonctionnalité}/design.md"
   ```
2. Lire le document requirements pour le contexte
3. Valider la solidité technique, la qualité architecturale et l’exhaustivité
4. Vérifier l’alignement avec les standards tech.md et les conventions structure.md
5. Vérifier la bonne réutilisation du code existant et les points d’intégration
6. Noter la qualité globale : PASS, NEEDS_IMPROVEMENT ou MAJOR_ISSUES

Si la validation échoue, utilisez les retours pour améliorer la conception avant de la présenter à l’utilisateur.
```

- **Présentez à l’utilisateur uniquement après validation ou amélioration**
- **Présentez le document design validé** avec les points de réutilisation du code et l’alignement avec les documents de pilotage
- Demandez : "La conception vous convient-elle ? Si oui, nous pouvons passer à la planification de l’implémentation."
- **CRITIQUE** : Attendez une approbation explicite avant de passer à la phase 3

## PHASE 3 : Création des tâches

**Modèle à suivre** : Chargez et utilisez la structure exacte du modèle tasks :

```bash
# Windows :
claude-code-spec-workflow get-content "C:\path\to\project\.claude\templates\tasks-template.md"

# macOS/Linux :
claude-code-spec-workflow get-content "/path/to/project/.claude/templates/tasks-template.md"
```

### Processus de planification des tâches
1. **Charger les phases précédentes**
   - Vérifiez que design.md existe et est approuvé
   - Chargez requirements.md et design.md pour le contexte complet :

   ```bash
   # Charger tous les documents de spécification complétés
   claude-code-spec-workflow get-spec-context {nom-fonctionnalité}
   ```

   **Note** : Cela charge les requirements.md et design.md créés précédemment.

2. **Générer la liste de tâches atomiques**
   - Décomposez la conception en tâches de codage atomiques selon ces critères :

   **Critères de tâche atomique** :
   - **Portée fichier** : Chaque tâche touche 1 à 3 fichiers liés maximum
   - **Limite de temps** : Réalisable en 15-30 minutes par un développeur expérimenté
   - **But unique** : Un résultat testable par tâche
   - **Fichiers spécifiques** : Doit spécifier les fichiers à créer/modifier
   - **Agent-friendly** : Entrée/sortie claire avec peu de changement de contexte

   **Exemples de granularité de tâche** :
   - MAUVAIS : "Implémenter le système d’authentification"
   - BON : "Créer le modèle User dans models/user.py avec les champs email/password"
   - MAUVAIS : "Ajouter les fonctionnalités de gestion utilisateur"
   - BON : "Ajouter l’utilitaire de hashage de mot de passe dans utils/auth.py avec bcrypt"

   **Directives d’implémentation** :
   - **Respectez structure.md** : Les tâches doivent respecter l’organisation des fichiers du projet
   - **Priorisez l’extension/adaptation du code existant** plutôt que de tout recréer
   - Utilisez le format checkbox avec hiérarchie numérotée
   - Chaque tâche doit référencer des exigences spécifiques ET le code existant à réutiliser
   - Concentrez-vous UNIQUEMENT sur les tâches de codage (pas de déploiement, test utilisateur, etc.)
   - Décomposez les concepts larges en opérations au niveau fichier

### Utilisation du modèle tasks
- **Lire et suivre** : Chargez le modèle tasks avec :
  ```bash
  # Windows : claude-code-spec-workflow get-content "C:\path\to\project\.claude\templates\tasks-template.md"
  # macOS/Linux : claude-code-spec-workflow get-content "/path/to/project/.claude/templates/tasks-template.md"
  ```
- **Utilisez la structure exacte** : Respectez toutes les sections et la mise en forme du modèle
- **Utilisez le format checkbox** : Respectez le format exact avec référence aux exigences

### Validation et approbation des tâches
- **Validation automatique** : Utilisez l’agent `spec-task-validator` pour valider les tâches :

```
Utilisez l’agent spec-task-validator pour valider la décomposition des tâches de la spécification {nom-fonctionnalité}.

L’agent doit :
1. Lire le document tasks avec le script get-content :
   ```bash
   # Windows :
   claude-code-spec-workflow get-content "C:\path\to\project\.sdd\specs\{nom-fonctionnalité}\tasks.md"

   # macOS/Linux :
   claude-code-spec-workflow get-content "/path/to/project/.sdd/specs/{nom-fonctionnalité}/tasks.md"
   ```
2. Lire requirements.md et design.md pour le contexte
3. Valider chaque tâche selon les critères d’atomicité (portée fichier, limite de temps, but unique)
4. Vérifier le format agent-friendly et la clarté des spécifications
5. Vérifier la justesse des références aux exigences et au code réutilisé
6. Noter la qualité globale : PASS, NEEDS_IMPROVEMENT ou MAJOR_ISSUES

Si la validation échoue, décomposez davantage les tâches avant de présenter.
```

- **Si la validation échoue** : Décomposez les tâches trop larges avant de présenter
- **Présentez à l’utilisateur uniquement après validation ou amélioration**

- **Présentez la liste de tâches validée**
- Demandez : "Les tâches vous conviennent-elles ? Chaque tâche doit être atomique et agent-friendly."
- **CRITIQUE** : Attendez une approbation explicite avant de poursuivre
- **APRÈS APPROBATION** : Demandez "Souhaitez-vous que je génère les commandes de tâches individuelles pour faciliter l’exécution ? (oui/non)"
- **SI OUI** : Exécutez `claude-code-spec-workflow generate-task-commands {nom-fonctionnalité}`
- **SI NON** : Continuez avec l’approche traditionnelle d’exécution des tâches

## Règles critiques du workflow

### Règles universelles
- **Ne créez qu’UNE seule spec à la fois**
- **Utilisez toujours le kebab-case pour les noms de fonctionnalités**
- **OBLIGATOIRE** : Analysez toujours la base de code avant chaque phase
- **Respectez exactement la structure des modèles** des fichiers spécifiés
- **Ne poursuivez pas sans approbation explicite de l’utilisateur** entre les phases
- **Ne sautez pas de phases** - complétez la séquence Exigences → Conception → Tâches → Commandes

### Exigences d’approbation
- **NE JAMAIS** passer à la phase suivante sans approbation explicite
- Acceptez uniquement les réponses affirmatives claires : "oui", "approuvé", "c’est bon", etc.
- Si l’utilisateur donne un retour, faites les révisions et demandez à nouveau l’approbation
- Continuez le cycle de révision jusqu’à obtention de l’approbation explicite

### Utilisation des modèles
**Utilisez le contexte des modèles préchargé** à l’étape 2 ci-dessus - ne rechargez pas les modèles.

- **Exigences** : Doivent suivre exactement la structure du modèle requirements
- **Conception** : Doit suivre exactement la structure du modèle design
- **Tâches** : Doivent suivre exactement la structure du modèle tasks
- **Incluez toutes les sections des modèles** - n’omettez aucune section requise
- **Référencez les modèles chargés** - tous les modèles de spécification ont été chargés au début

### Génération des commandes de tâches
- **Demandez UNIQUEMENT après validation de tasks.md**
- **Utilisez la commande NPX** : `claude-code-spec-workflow generate-task-commands {nom-fonctionnalité}`
- **Choix utilisateur** : Demandez toujours à l’utilisateur s’il souhaite générer les commandes (oui/non)
- **Redémarrage requis** : Informez l’utilisateur de redémarrer Claude Code pour voir les nouvelles commandes

## Gestion des erreurs

Si des problèmes surviennent durant le workflow :
- **Exigences floues** : Posez des questions ciblées pour clarifier
- **Conception trop complexe** : Suggérez de découper en composants plus petits
- **Tâches trop larges** : Décomposez en tâches plus atomiques
- **Blocage d’implémentation** : Documentez le blocage et proposez des alternatives
- **Modèle introuvable** : Informez que les modèles doivent être générés lors de la mise en place

## Critères de réussite

Un workflow de spec réussi inclut :
- [x] Exigences complètes avec user stories et critères d’acceptation (modèle requirements)
- [x] Conception complète avec architecture et composants (modèle design)
- [x] Décomposition détaillée des tâches avec références aux exigences (modèle tasks)
- [x] Toutes les phases explicitement approuvées avant de poursuivre
- [x] Commandes de tâches générées (si choix utilisateur)
- [x] Prêt pour la phase d’implémentation

## Exemple d’utilisation
```
/spec-create user-authentication "Permettre aux utilisateurs de s’inscrire et de se connecter en toute sécurité"
```

## Phase d’implémentation
Après avoir complété toutes les phases et généré les commandes de tâches, affichez les informations suivantes à l’utilisateur :
0. **REDÉMARREZ Claude Code** pour voir les nouvelles commandes
1. **Utilisez les commandes de tâches individuelles** : `/user-authentication-task-1`, `/user-authentication-task-2`, etc.
2. **Ou utilisez spec-execute** : Exécutez les tâches individuellement selon les besoins
3. **Suivez la progression** : Utilisez `/spec-status user-authentication` pour suivre l’avancement
