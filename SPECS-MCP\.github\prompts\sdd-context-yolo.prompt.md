---
mode: agent
model: <PERSON> 4
tools: ['codebase', 'search', 'findTestFiles']
description: Génère automatiquement les 3 fichiers de contexte projet SDD en une seule requête sans interruption ni validation.
---

# Génération Automatique du Contexte Projet SDD

Tu vas maintenant exécuter le processus de génération automatique complète de la documentation contexte projet selon la méthodologie Spec-Driven Development (SDD).

## Objectif

Créer la mémoire centrale du projet qui servira de référence unique pour tous les développements ultérieurs en générant automatiquement les 3 fichiers contexte :

- `.sdd/project/product.md` - Documentation produit et vision
- `.sdd/project/structure.md` - Architecture et organisation du projet  
- `.sdd/project/tech.md` - Spécifications techniques et implémentation

## Workflow Automatique Complet

### Règle Fondamentale
**Génération automatique complète des 3 fichiers en une seule requête sans interruption, aucune validation ne doit être requise.**

### Étapes à Exécuter Automatiquement

#### 1. Génération Product (product.md)
- **Vérifier** l'existence du répertoire `.sdd/project/`
- **Créer le fichier** `.sdd/project/product.md`
- **Analyser** le contexte métier et utilisateurs en profondeur
- **Définir** des métriques mesurables et objectifs SMART
- **Générer toutes les sections** sans questions séquentielles :
  - Vision et mission du produit
  - Objectifs stratégiques avec métriques SMART
  - Utilisateurs cibles et personas
  - Fonctionnalités principales et roadmap
  - Critères de succès mesurables

#### 2. Génération Structure (structure.md)
- **Créer le fichier** `.sdd/project/structure.md`
- **Analyser** l'organisation optimale selon le type de projet
- **Définir** des conventions claires et cohérentes
- **Intégrer** avec les bonnes pratiques SDD
- **Générer toutes les sections** automatiquement :
  - Architecture des dossiers et organisation
  - Conventions de nommage et standards
  - Patterns et bonnes pratiques
  - Intégration méthodologie SDD
  - Structure évolutive et maintenable

#### 3. Génération Tech (tech.md)
- **Créer le fichier** `.sdd/project/tech.md`
- **Analyser** les spécifications techniques détaillées
- **Identifier** les contraintes et exigences non-fonctionnelles
- **Définir** la configuration et architecture technique
- **Générer toutes les sections** automatiquement :
  - Stack technique et technologies
  - Configuration et environnements
  - APIs et interfaces
  - Contraintes et exigences non-fonctionnelles
  - Standards de développement

## Principes de Génération

### Méthodologie Mémoire
- **Centralisation** : Toute l'information critique centralisée dans `.sdd/project/`
- **Référencement** : Les templates servent de modèles canoniques
- **Cohérence** : Une seule source de vérité maintenue
- **Traçabilité** : Liens explicites entre tous les documents
- **Évolutivité** : Structure permettant les mises à jour continues

### Contraintes Techniques
- **Destination Obligatoire** : `.sdd/project/`
- **Nommage Standard** : `product.md`, `structure.md`, `tech.md`
- **Analyse Approfondie** : Examiner l'ensemble du contexte projet disponible

## Critères de Qualité

### Standards Automatiques
- Conformité aux templates de référence
- Cohérence inter-documents
- Complétude des sections obligatoires
- Application des instructions spécifiques de chaque template
- Respect de la structure et organisation prescrites
- Maintien de la cohérence et traçabilité
- Création de références croisées appropriées

## Approche de Travail

### Analyse Approfondie
- Examiner tous les fichiers existants du projet
- Identifier les patterns et conventions établies
- Comprendre les spécificités du domaine métier
- Intégrer les contraintes techniques existantes

### Génération Intelligente
- Créer du contenu substantiel et pertinent pour chaque section
- Éviter les placeholders génériques
- Personnaliser selon le contexte projet spécifique
- Maintenir la cohérence entre les 3 documents

### Finalisation Automatique
- S'assurer de la complétude des 3 fichiers
- Vérifier la cohérence inter-documents
- Valider l'alignement avec les bonnes pratiques
- Documenter les décisions et rationales

## Instructions d'Exécution

**COMMENCER MAINTENANT** la génération automatique complète :

1. **Vérifier** l'existence du répertoire `.sdd/project/`
2. **Analyser** l'ensemble du contexte projet disponible
3. **Générer automatiquement** les 3 fichiers en séquence :
   - `.sdd/project/product.md`
   - `.sdd/project/structure.md`
   - `.sdd/project/tech.md`
4. **Finaliser** avec la documentation complète du contexte projet

**Résultat Attendu** : 3 fichiers contexte complets créés en une seule requête, servant de base de référence pour toutes les spécifications futures selon la méthodologie SDD.

**IMPORTANT** : Aucune validation utilisateur n'est requise. Exécuter le processus de génération complète automatiquement sans interruption.