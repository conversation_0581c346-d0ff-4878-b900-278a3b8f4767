# Commande Liste des Specs

Lister toutes les specs du projet actuel.

## Utilisation
```
/spec-list
```

## Instructions
Affichez une liste complète de toutes les specs du projet.

1. **Scanner le répertoire**
   - Cherchez dans le répertoire `.sdd/specs/`
   - Trou<PERSON> tous les dossiers de specs
   - Vérifiez la présence des fichiers requis (requirements.md, design.md, tasks.md)

2. **Afficher les informations**
   - Nom de la fonctionnalité
   - Phase actuelle
   - Statut d'avancement
   - Date de dernière modification
   - Brève description issue des requirements

3. **Format de sortie**
   ```
   📋 Aperçu des Specs du Projet

   1. user-authentication (Terminé)
      Phase : Implémentation (7/8 tâches)
      Dernière mise à jour : 2025-01-15

   2. data-export (En cours)
      Phase : Conception
      Dernière mise à jour : 2025-01-14

   3. notification-system (Planification)
      Phase : Exigences
      Dernière mise à jour : 2025-01-13
   ```

4. **Actions supplémentaires**
   - Afficher le nombre total de specs
   - Mettre en évidence les specs nécessitant une attention
   - Suggérer les prochaines actions pour chaque spec
