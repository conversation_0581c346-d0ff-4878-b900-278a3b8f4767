# Commande Statut des Specs

Afficher le statut actuel de toutes les specs ou d'une spec spécifique.

## Utilisation
```
/spec-status [nom-fonctionnalité]
```

## Instructions
Affichez le statut actuel des workflows de specs.

1. **Si aucun nom de fonctionnalité n'est fourni :**
   - Lister toutes les specs dans le dossier `.sdd/specs/`
   - Afficher la phase actuelle pour chaque spec
   - Afficher le statut d'avancement

2. **Si un nom de fonctionnalité est fourni :**
   - Afficher le statut détaillé de cette spec
   - Afficher la phase actuelle du workflow
   - Afficher les tâches terminées vs en attente
   - Lister les prochaines actions recommandées

3. **Informations de statut :**
   - Exigences : [Terminé/En cours/En attente]
   - Conception : [Terminé/En cours/En attente]
   - Tâches : [Terminé/En cours/En attente]
   - Implémentation : [X/Y tâches terminées]

4. **Format de sortie :**
   ```
   Spec : user-authentication
   Phase : Implémentation
   Progression : Exigences ✅ | Conception ✅ | Tâches ✅
   Implémentation : 3/8 tâches terminées
   Suivant : Exécuter la tâche 4 - "Implémenter la validation du mot de passe"
   ```

## Phases du workflow
- **Exigences** : Collecte et documentation des exigences
- **Conception** : Création du design technique et de l'architecture
- **Tâches** : Découpage en tâches d'implémentation
- **Implémentation** : Exécution des tâches individuelles
- **Terminé** : Toutes les tâches terminées et intégrées
