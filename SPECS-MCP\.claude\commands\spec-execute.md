# Commande Spec Execute

Exécutez des tâches spécifiques à partir de la liste de tâches approuvée.

## Utilisation
```
/spec-execute [id-tâche] [nom-fonctionnalité]
```

## Vue d'ensemble de la phase
**Votre rôle** : Exécuter les tâches de manière systématique avec validation

Ceci est la Phase 4 du workflow de spécification. Votre objectif est d’implémenter les tâches individuelles de la liste de tâches approuvée, une par une.

## Instructions

**Étapes d’exécution** :

**Étape 1 : Charger le contexte**
```bash
# Charger les documents d’orientation (si disponibles)
claude-code-spec-workflow get-steering-context

# Charger le contexte de la spécification
claude-code-spec-workflow get-spec-context {nom-fonctionnalité}

# Charger les détails de la tâche spécifique
claude-code-spec-workflow get-tasks {nom-fonctionnalité} {id-tâche} --mode single
```

**Étape 2 : Exécuter avec l’agent**
Utilisez l’agent `spec-task-executor` :
```
Utilisez l’agent spec-task-executor pour implémenter la tâche {id-tâche} pour la spécification {nom-fonctionnalité}.

## Contexte d’orientation
[COLLEZ ICI LA SORTIE COMPLÈTE DE LA COMMANDE get-steering-context]

## Contexte de la spécification
[COLLEZ ICI LES SECTIONS EXIGENCES ET CONCEPTION DE LA COMMANDE get-spec-context]

## Détails de la tâche
[COLLEZ ICI LA SORTIE DE LA COMMANDE get-tasks SINGLE]

## Instructions
- Implémentez UNIQUEMENT la tâche spécifiée : {id-tâche}
- Respectez toutes les conventions du projet et exploitez le code existant
- Marquez la tâche comme terminée avec : claude-code-spec-workflow get-tasks {nom-fonctionnalité} {id-tâche} --mode complete
- Fournissez un résumé de la complétion
```


3. **Exécution de la tâche**
   - Concentrez-vous sur UNE tâche à la fois
   - Si la tâche comporte des sous-tâches, commencez par celles-ci
   - Suivez les détails d’implémentation du fichier design.md
   - Vérifiez par rapport aux exigences spécifiées dans la tâche

4. **Directives d’implémentation**
   - Écrivez un code propre et maintenable
   - **Suivez les documents d’orientation** : Respectez les modèles dans tech.md et les conventions dans structure.md
   - Respectez les modèles et conventions du code existant
   - Incluez une gestion des erreurs appropriée
   - Ajoutez des tests unitaires si spécifié
   - Documentez la logique complexe

5. **Validation**
   - Vérifiez que l’implémentation répond aux critères d’acceptation
   - Exécutez les tests s’ils existent
   - Vérifiez les erreurs de lint/type
   - Assurez l’intégration avec le code existant

6. **Protocole de complétion de la tâche**
Lors de la complétion d’une tâche via `/spec-execute` :
   1. **Marquez la tâche comme terminée** : Utilisez le script get-tasks pour marquer la complétion :
      ```bash
      # Commande multiplateforme :
      claude-code-spec-workflow get-tasks {nom-fonctionnalité} {id-tâche} --mode complete
      ```
   2. **Confirmez à l’utilisateur** : Indiquez clairement "La tâche X a été marquée comme terminée"
   3. **Arrêtez l’exécution** : Ne passez pas automatiquement à la tâche suivante
   4. **Attendez l’instruction** : Laissez l’utilisateur décider des prochaines étapes




## Règles critiques du workflow

### Exécution des tâches
- N’exécutez **QU’UNE** tâche à la fois lors de l’implémentation
- **CRITIQUE** : Marquez les tâches terminées avec get-tasks --mode complete avant d’arrêter
- **TOUJOURS** arrêter après avoir terminé une tâche
- **JAMAIS** passer automatiquement à la tâche suivante
- **DOIT** attendre que l’utilisateur demande l’exécution de la prochaine tâche
- **CONFIRMEZ** le statut de complétion de la tâche à l’utilisateur

### Références aux exigences
- **TOUTES** les tâches doivent référencer des exigences spécifiques au format _Exigences : X.Y_
- **ASSUREZ** la traçabilité des exigences à travers la conception jusqu’à l’implémentation
- **VALIDEZ** les implémentations par rapport aux exigences référencées

## Sélection de la tâche
Si aucun id-tâche n’est spécifié :
- Consultez tasks.md pour la spécification
- Recommandez la prochaine tâche en attente
- Demandez à l’utilisateur de confirmer avant de poursuivre

Si aucun nom-fonctionnalité n’est spécifié :
- Vérifiez le répertoire `.sdd/specs/` pour les spécifications disponibles
- S’il n’existe qu’une seule spécification, utilisez-la
- S’il y a plusieurs spécifications, demandez à l’utilisateur laquelle utiliser
- Affichez une erreur si aucune spécification n’est trouvée

## Exemples
```
/spec-execute 1 authentification-utilisateur
/spec-execute 2.1 authentification-utilisateur
```

## Règles importantes
- N’exécutez qu’UNE tâche à la fois
- **TOUJOURS** marquez les tâches terminées avec get-tasks --mode complete
- Arrêtez toujours après avoir terminé une tâche
- Attendez l’approbation de l’utilisateur avant de continuer
- Ne sautez jamais de tâches ni ne passez outre
- Confirmez le statut de complétion de la tâche à l’utilisateur

## Prochaines étapes
Après la complétion d’une tâche, vous pouvez :
- Traiter les problèmes identifiés lors de la revue
- Exécuter les tests si applicable
- Exécuter la prochaine tâche avec `/spec-execute [id-tâche-suivante]`
- Vérifier la progression globale avec `/spec-status {nom-fonctionnalité}`
