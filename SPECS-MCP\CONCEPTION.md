# CONCEPTION.md - Serveur MCP pour Workflow SDD

## Vue d'ensemble

Ce document présente la conception et l'architecture pour la création d'un serveur MCP (Model Context Protocol) supportant un workflow de développement SDD (Spec Driven Development). L'objectif est d'adapter l'architecture existante basée sur des agents CLI vers un serveur MCP exposant des outils et prompts pour permettre à un LLM de suivre le même workflow SDD de manière fluide et automatisée.

## Analyse du Système Existant

### Architecture Actuelle

Le système existant fonctionne avec une architecture modulaire comprenant :

#### 1. Agents Spécialisés
- **spec-requirements-validator** : Valide les documents d'exigences selon des critères de qualité stricte
- **spec-design-validator** : Valide la conception technique et l'architecture
- **spec-task-validator** : Valide la décomposition en tâches atomiques
- **spec-task-executor** : Exécute les tâches individuelles de manière séquentielle

#### 2. Commandes de Workflow
- **spec-create** : Workflow complet de création (exigences → conception → tâches)
- **spec-execute** : Exécution de tâches spécifiques
- **spec-status** : Suivi de l'état d'avancement
- **spec-list** : Liste des spécifications du projet
- **spec-project-setup** : Configuration des documents de pilotage

#### 3. Templates Structurés
- **requirements-template.md** : Structure pour les exigences avec user stories et critères d'acceptation
- **design-template.md** : Architecture technique et modèles de données
- **tasks-template.md** : Décomposition en tâches atomiques
- **tech-template.md** : Pile technologique et standards
- **structure-template.md** : Organisation du projet et conventions
- **product-template.md** : Vision produit et objectifs métier

### Flux de Travail SDD

```mermaid
graph TD
    A[Initialisation] --> B[Phase 1: Exigences]
    B --> C[Validation Requirements]
    C --> D{Validation OK?}
    D -->|Non| B
    D -->|Oui| E[Phase 2: Conception]
    E --> F[Validation Design]
    F --> G{Validation OK?}
    G -->|Non| E
    G -->|Oui| H[Phase 3: Tâches]
    H --> I[Validation Tasks]
    I --> J{Validation OK?}
    J -->|Non| H
    J -->|Oui| K[Phase 4: Exécution]
    K --> L[Exécution Séquentielle]
    L --> M[Terminé]
```

## Proposition Principale : Architecture MCP Orchestrée

### Vue d'ensemble de l'Architecture

Cette approche privilégie une orchestration centralisée avec un serveur MCP unique gérant l'ensemble du workflow SDD.

```mermaid
graph TB
    subgraph "MCP Client (LLM Host)"
        LLM[Claude/GPT-4]
        CLIENT[MCP Client]
        LLM <--> CLIENT
    end
    
    subgraph "Serveur MCP SDD"
        CORE[Core Engine]
        WM[Workflow Manager]
        PROMPT[Prompt Orchestrator]
        VALID[Validation Services]
        STATE[State Manager]
        TEMPL[Template Engine]
        
        CORE <--> WM
        CORE <--> PROMPT
        CORE <--> VALID
        CORE <--> STATE
        CORE <--> TEMPL
    end
    
    subgraph "Système de Fichiers"
        SPECS[Spécifications]
        TEMPLATES[Templates]
        PROJECTS[Projets]
    end
    
    CLIENT <-->|JSON-RPC 2.0| CORE
    CORE <--> SPECS
    CORE <--> TEMPLATES
    CORE <--> PROJECTS
```

### Composants Principaux

#### 1. Core Engine MCP
Point d'entrée unique implémentant le protocole MCP standard :
- Gestion des connexions JSON-RPC 2.0
- Routage des requêtes vers les modules appropriés
- Gestion des erreurs et logging
- Interface standardisée avec les LLM clients

#### 2. Workflow Manager
Orchestrateur central du flux SDD :
- Maintien de l'état global du projet
- Gestion des transitions entre phases
- Coordination des validations
- Suivi de l'historique des modifications

#### 3. Prompt Orchestrator
Générateur intelligent de prompts contextuels :
- Adaptation dynamique selon la phase courante
- Injection du contexte pertinent (templates, état projet)
- Personnalisation selon les préférences utilisateur
- Gestion des prompts multi-tours

#### 4. Validation Services
Services de validation automatisée :
- Implémentation des règles de validation existantes
- Validation syntaxique et sémantique
- Vérification de la cohérence inter-phases
- Génération de rapports détaillés

#### 5. State Manager
Gestionnaire d'état persistant :
- Sauvegarde de l'état projet en temps réel
- Gestion des versions et historique
- Mécanismes de rollback
- Synchronisation multi-utilisateur

#### 6. Template Engine
Moteur de templates avancé :
- Chargement dynamique des templates
- Personnalisation et adaptation contextuelle
- Génération de structures pré-remplies
- Validation des templates

### Outils MCP Exposés

#### Outils de Gestion de Projet

1. **sdd_project_init**
   ```json
   {
     "name": "sdd_project_init",
     "description": "Initialise un nouveau projet SDD avec la structure de base",
     "parameters": {
       "project_name": {"type": "string", "description": "Nom du projet"},
       "description": {"type": "string", "description": "Description du projet"},
       "tech_stack": {"type": "array", "items": {"type": "string"}}
     }
   }
   ```

2. **sdd_project_status**
   ```json
   {
     "name": "sdd_project_status",
     "description": "Obtient l'état actuel du projet SDD",
     "parameters": {
       "project_id": {"type": "string", "description": "Identifiant du projet"}
     }
   }
   ```

#### Outils de Workflow

3. **sdd_spec_create**
   ```json
   {
     "name": "sdd_spec_create",
     "description": "Démarre la création d'une nouvelle spécification",
     "parameters": {
       "feature_name": {"type": "string", "description": "Nom de la fonctionnalité"},
       "initial_description": {"type": "string", "description": "Description initiale"}
     }
   }
   ```

4. **sdd_phase_advance**
   ```json
   {
     "name": "sdd_phase_advance",
     "description": "Fait progresser une spécification vers la phase suivante",
     "parameters": {
       "spec_id": {"type": "string", "description": "ID de la spécification"},
       "validation_required": {"type": "boolean", "default": true}
     }
   }
   ```

#### Outils de Validation

5. **sdd_validate_requirements**
   ```json
   {
     "name": "sdd_validate_requirements",
     "description": "Valide un document d'exigences",
     "parameters": {
       "spec_id": {"type": "string", "description": "ID de la spécification"},
       "content": {"type": "string", "description": "Contenu à valider"}
     }
   }
   ```

6. **sdd_validate_design**
   ```json
   {
     "name": "sdd_validate_design",
     "description": "Valide un document de conception",
     "parameters": {
       "spec_id": {"type": "string", "description": "ID de la spécification"},
       "content": {"type": "string", "description": "Contenu à valider"}
     }
   }
   ```

7. **sdd_validate_tasks**
   ```json
   {
     "name": "sdd_validate_tasks",
     "description": "Valide la décomposition en tâches",
     "parameters": {
       "spec_id": {"type": "string", "description": "ID de la spécification"},
       "content": {"type": "string", "description": "Contenu à valider"}
     }
   }
   ```

#### Outils d'Exécution

8. **sdd_execute_task**
   ```json
   {
     "name": "sdd_execute_task",
     "description": "Exécute une tâche spécifique",
     "parameters": {
       "spec_id": {"type": "string", "description": "ID de la spécification"},
       "task_id": {"type": "string", "description": "ID de la tâche"},
       "context": {"type": "object", "description": "Contexte d'exécution"}
     }
   }
   ```

### Prompts MCP Structurés

#### 1. Prompt de Création d'Exigences
```markdown
# CRÉATION D'EXIGENCES - Phase 1 du SDD

## CONTEXTE PROJET
{{project_context}}

## INSTRUCTIONS
Vous devez créer un document d'exigences suivant le template requirements-template.md.

### STRUCTURE REQUISE
1. Introduction et alignement avec la vision produit
2. Exigences avec user stories au format : "En tant que [rôle], je veux [fonctionnalité], afin de [bénéfice]"
3. Critères d'acceptation au format EARS (QUAND/SI/ALORS)
4. Exigences non fonctionnelles

### CRITÈRES DE QUALITÉ
- Toutes les exigences doivent être spécifiques, mesurables et testables
- Les user stories doivent couvrir tous les personas principaux
- Les critères d'acceptation doivent inclure cas nominaux et d'erreur

### TEMPLATE DE RÉFÉRENCE
{{requirements_template}}

### VALIDATION AUTOMATIQUE
Votre sortie sera automatiquement validée selon les critères du spec-requirements-validator.
```

#### 2. Prompt de Conception
```markdown
# CONCEPTION TECHNIQUE - Phase 2 du SDD

## CONTEXTE
{{project_context}}
{{requirements_summary}}

## INSTRUCTIONS
Créez un document de conception technique basé sur les exigences validées.

### STRUCTURE REQUISE
1. Vue d'ensemble et alignement avec tech.md
2. Architecture et diagrammes Mermaid
3. Composants et interfaces
4. Modèles de données
5. Gestion des erreurs
6. Stratégie de tests

### CONTRAINTES TECHNIQUES
{{tech_constraints}}

### RÉUTILISATION DE CODE
{{existing_components}}

### TEMPLATE DE RÉFÉRENCE
{{design_template}}
```

### Gestion d'État

#### Modèle d'État Projet
```json
{
  "project_id": "uuid",
  "name": "nom_projet",
  "created_at": "timestamp",
  "updated_at": "timestamp",
  "tech_stack": ["technologie1", "technologie2"],
  "specifications": [
    {
      "spec_id": "uuid",
      "feature_name": "nom_fonctionnalité",
      "current_phase": "requirements|design|tasks|execution|completed",
      "status": "in_progress|validation_pending|validated|rejected",
      "phases": {
        "requirements": {
          "content": "contenu_markdown",
          "validation_status": "pending|validated|rejected",
          "validation_report": {},
          "created_at": "timestamp",
          "updated_at": "timestamp"
        },
        "design": {
          "content": "contenu_markdown",
          "validation_status": "pending|validated|rejected",
          "validation_report": {},
          "created_at": "timestamp",
          "updated_at": "timestamp"
        },
        "tasks": {
          "content": "contenu_markdown",
          "validation_status": "pending|validated|rejected",
          "validation_report": {},
          "task_list": [
            {
              "task_id": "string",
              "description": "string",
              "status": "pending|in_progress|completed",
              "execution_log": []
            }
          ],
          "created_at": "timestamp",
          "updated_at": "timestamp"
        }
      },
      "execution_history": []
    }
  ]
}
```

### Avantages de cette Approche

1. **Centralisation** : Un seul point d'entrée pour l'ensemble du workflow
2. **Cohérence** : Gestion centralisée de l'état et des transitions
3. **Simplicité** : Interface unifiée pour le LLM client
4. **Extensibilité** : Architecture modulaire permettant l'ajout de nouvelles fonctionnalités
5. **Traçabilité** : Historique complet des actions et états

### Défis et Solutions

1. **Complexité d'orchestration** → Architecture modulaire avec interfaces claires
2. **Gestion d'état complexe** → Persistence structurée avec versioning
3. **Performance** → Cache intelligent et traitements asynchrones
4. **Évolutivité** → Architecture événementielle et modules découplés

## Proposition Alternative : Architecture MCP Distribuée

### Vue d'ensemble

Cette approche privilégie une architecture distribuée avec des serveurs MCP spécialisés pour chaque aspect du workflow SDD.

```mermaid
graph TB
    subgraph "MCP Client (LLM Host)"
        LLM[Claude/GPT-4]
        CLIENT[MCP Client]
        LLM <--> CLIENT
    end
    
    subgraph "Serveurs MCP Spécialisés"
        MCP_REQ[MCP Requirements]
        MCP_DESIGN[MCP Design]
        MCP_TASKS[MCP Tasks]
        MCP_EXEC[MCP Executor]
        MCP_COORD[MCP Coordinator]
    end
    
    subgraph "Services Partagés"
        STATE[State Service]
        VALID[Validation Service]
        TEMPL[Template Service]
        FILE[File System]
    end
    
    CLIENT <--> MCP_COORD
    MCP_COORD <--> MCP_REQ
    MCP_COORD <--> MCP_DESIGN
    MCP_COORD <--> MCP_TASKS
    MCP_COORD <--> MCP_EXEC
    
    MCP_REQ <--> STATE
    MCP_DESIGN <--> STATE
    MCP_TASKS <--> STATE
    MCP_EXEC <--> STATE
    
    MCP_REQ <--> VALID
    MCP_DESIGN <--> VALID
    MCP_TASKS <--> VALID
    
    MCP_REQ <--> TEMPL
    MCP_DESIGN <--> TEMPL
    MCP_TASKS <--> TEMPL
    
    STATE <--> FILE
    TEMPL <--> FILE
```

### Serveurs MCP Spécialisés

#### 1. MCP Requirements Server
**Responsabilité** : Gestion exclusive de la phase d'exigences
- Outils : `create_requirements`, `validate_requirements`, `update_requirements`
- Prompts : Templates d'exigences contextualisés
- Validation : Règles de qualité des user stories et critères d'acceptation

#### 2. MCP Design Server
**Responsabilité** : Gestion de la conception technique
- Outils : `create_design`, `validate_design`, `generate_diagrams`
- Prompts : Templates de conception avec contraintes techniques
- Validation : Cohérence architecturale et alignment avec les exigences

#### 3. MCP Tasks Server
**Responsabilité** : Décomposition en tâches atomiques
- Outils : `create_tasks`, `validate_tasks`, `decompose_feature`
- Prompts : Guides de décomposition en tâches
- Validation : Atomicité et faisabilité des tâches

#### 4. MCP Executor Server
**Responsabilité** : Exécution des tâches
- Outils : `execute_task`, `monitor_execution`, `rollback_task`
- Prompts : Contexte d'exécution et guidelines
- Validation : Conformité et qualité du code généré

#### 5. MCP Coordinator
**Responsabilité** : Orchestration et coordination
- Outils : `start_workflow`, `transition_phase`, `get_status`
- Prompts : Guidance du workflow global
- Coordination entre les serveurs spécialisés

### Services Partagés

#### State Service
- API REST pour la gestion d'état
- Persistence des données projet
- Synchronisation entre serveurs
- Gestion des versions et historique

#### Validation Service
- Moteur de validation centralisé
- Règles configurables par phase
- Rapports de validation détaillés
- Métriques de qualité

#### Template Service
- Gestion centralisée des templates
- Versioning et personnalisation
- API de génération de contenu
- Cache intelligent

### Avantages de l'Approche Distribuée

1. **Spécialisation** : Chaque serveur MCP se concentre sur son domaine
2. **Scalabilité** : Possibilité de scaler indépendamment chaque service
3. **Maintenabilité** : Code plus modulaire et focalisé
4. **Flexibilité** : Remplacement ou mise à jour indépendante des services
5. **Parallélisme** : Possibilité d'exécution parallèle de certaines phases

### Défis de l'Approche Distribuée

1. **Complexité de déploiement** : Multiple serveurs à gérer
2. **Synchronisation** : Coordination entre services distribués
3. **Latence** : Communications inter-services
4. **Débug complexe** : Traçabilité across multiple services
5. **Configuration** : Gestion de la configuration distribuée

## Comparaison des Approches

| Critère | Approche Orchestrée | Approche Distribuée |
|---------|-------------------|-------------------|
| **Simplicité d'implémentation** | ⭐⭐⭐⭐ | ⭐⭐ |
| **Simplicité de déploiement** | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| **Scalabilité** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Maintenabilité** | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Performance** | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Flexibilité** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Débug/Traçabilité** | ⭐⭐⭐⭐ | ⭐⭐ |
| **Cohérence des données** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

## Recommandation

### Approche Recommandée : Orchestrée

Je recommande l'**approche orchestrée** pour les raisons suivantes :

1. **Simplicité initiale** : Plus facile à implémenter et déployer comme MVP
2. **Cohérence garantie** : Gestion centralisée de l'état et des transitions
3. **Expérience utilisateur fluide** : Interface unique pour le LLM
4. **Maintenance simplifiée** : Un seul service à gérer et débugger
5. **Compatibilité MCP** : Respect strict du protocole avec un seul serveur

### Migration Possible

L'architecture orchestrée peut évoluer vers une approche distribuée si les besoins de scalabilité le justifient :

1. **Phase 1** : Implémentation orchestrée monolithique
2. **Phase 2** : Extraction des modules internes en microservices
3. **Phase 3** : Distribution en serveurs MCP spécialisés si nécessaire

## Détails Techniques d'Implémentation

### Architecture Technique du Core Engine

#### Structure du Serveur MCP
```typescript
interface MCPServer {
  // Configuration du serveur
  config: ServerConfig;
  
  // Gestionnaires principaux
  connectionManager: ConnectionManager;
  requestRouter: RequestRouter;
  responseHandler: ResponseHandler;
  
  // Services métier
  workflowManager: WorkflowManager;
  stateManager: StateManager;
  validationService: ValidationService;
  templateEngine: TemplateEngine;
  promptOrchestrator: PromptOrchestrator;
}

interface ServerConfig {
  name: string;
  version: string;
  capabilities: ServerCapabilities;
  transports: TransportConfig[];
  storage: StorageConfig;
  validation: ValidationConfig;
}
```

#### Implémentation JSON-RPC 2.0
```typescript
class MCPRequestHandler {
  async handleRequest(request: JsonRpcRequest): Promise<JsonRpcResponse> {
    try {
      // Validation du format JSON-RPC
      this.validateJsonRpcFormat(request);
      
      // Routage vers le handler approprié
      const handler = this.getHandler(request.method);
      const result = await handler.execute(request.params);
      
      return {
        jsonrpc: "2.0",
        id: request.id,
        result: result
      };
    } catch (error) {
      return {
        jsonrpc: "2.0",
        id: request.id,
        error: {
          code: error.code,
          message: error.message,
          data: error.details
        }
      };
    }
  }
}
```

### Système de Persistence Avancé

#### Modèle de Données Détaillé
```sql
-- Schéma de base de données
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    tech_stack JSONB,
    config JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE specifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    feature_name VARCHAR(255) NOT NULL,
    current_phase spec_phase_enum DEFAULT 'requirements',
    status spec_status_enum DEFAULT 'in_progress',
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE spec_phases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    spec_id UUID REFERENCES specifications(id) ON DELETE CASCADE,
    phase_type spec_phase_enum NOT NULL,
    content TEXT,
    validation_status validation_status_enum DEFAULT 'pending',
    validation_report JSONB,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    spec_id UUID REFERENCES specifications(id) ON DELETE CASCADE,
    task_number VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    status task_status_enum DEFAULT 'pending',
    execution_log JSONB,
    dependencies VARCHAR[],
    files_affected VARCHAR[],
    estimated_duration INTERVAL,
    actual_duration INTERVAL,
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- Types énumérés
CREATE TYPE spec_phase_enum AS ENUM ('requirements', 'design', 'tasks', 'execution', 'completed');
CREATE TYPE spec_status_enum AS ENUM ('in_progress', 'validation_pending', 'validated', 'rejected');
CREATE TYPE validation_status_enum AS ENUM ('pending', 'validated', 'rejected');
CREATE TYPE task_status_enum AS ENUM ('pending', 'in_progress', 'completed', 'failed', 'skipped');
```

#### Service de Persistence
```typescript
class StateManager {
  private db: DatabaseConnection;
  private cache: CacheInterface;
  
  async saveProjectState(projectId: string, state: ProjectState): Promise<void> {
    const transaction = await this.db.beginTransaction();
    try {
      // Sauvegarde atomique avec versioning
      await this.saveProjectMetadata(transaction, projectId, state.metadata);
      await this.saveSpecifications(transaction, projectId, state.specifications);
      await this.saveWorkflowState(transaction, projectId, state.workflow);
      
      await transaction.commit();
      
      // Mise à jour du cache
      await this.cache.set(`project:${projectId}`, state, 3600); // 1h TTL
      
      // Événement de changement d'état
      this.emitStateChange(projectId, state);
    } catch (error) {
      await transaction.rollback();
      throw new StateManagerError('Failed to save project state', error);
    }
  }
  
  async getProjectState(projectId: string): Promise<ProjectState> {
    // Tentative de récupération depuis le cache
    const cached = await this.cache.get(`project:${projectId}`);
    if (cached) return cached;
    
    // Reconstruction depuis la base de données
    const state = await this.reconstructStateFromDB(projectId);
    
    // Mise en cache
    await this.cache.set(`project:${projectId}`, state, 3600);
    
    return state;
  }
}
```

### Moteur de Validation Avancé

#### Architecture des Validateurs
```typescript
interface ValidationRule {
  id: string;
  name: string;
  description: string;
  phase: SpecPhase;
  severity: 'error' | 'warning' | 'info';
  validate(content: string, context: ValidationContext): ValidationResult;
}

class ValidationEngine {
  private rules: Map<SpecPhase, ValidationRule[]>;
  private customValidators: Map<string, CustomValidator>;
  
  async validateContent(
    phase: SpecPhase, 
    content: string, 
    context: ValidationContext
  ): Promise<ValidationReport> {
    const rules = this.rules.get(phase) || [];
    const results: ValidationResult[] = [];
    
    // Validation parallèle des règles
    const validationPromises = rules.map(async rule => {
      try {
        return await rule.validate(content, context);
      } catch (error) {
        return {
          ruleId: rule.id,
          status: 'error',
          message: `Validation rule failed: ${error.message}`,
          line: 0,
          severity: 'error'
        };
      }
    });
    
    const allResults = await Promise.all(validationPromises);
    results.push(...allResults);
    
    // Génération du rapport
    return this.generateValidationReport(results, context);
  }
}

// Exemples de règles de validation
class RequirementsUserStoryRule implements ValidationRule {
  id = 'req-user-story-format';
  name = 'User Story Format';
  description = 'Vérifie le format des user stories';
  phase = SpecPhase.Requirements;
  severity = 'error' as const;
  
  validate(content: string, context: ValidationContext): ValidationResult {
    const userStoryPattern = /En tant que .+, je veux .+, afin de .+/g;
    const lines = content.split('\n');
    const errors: ValidationIssue[] = [];
    
    lines.forEach((line, index) => {
      if (line.includes('En tant que') && !userStoryPattern.test(line)) {
        errors.push({
          line: index + 1,
          message: 'Format de user story incorrect',
          severity: this.severity
        });
      }
    });
    
    return {
      ruleId: this.id,
      status: errors.length > 0 ? 'failed' : 'passed',
      issues: errors
    };
  }
}
```

### Système de Templates Dynamiques

#### Template Engine Avancé
```typescript
class TemplateEngine {
  private templates: Map<string, CompiledTemplate>;
  private partials: Map<string, TemplatePartial>;
  private helpers: Map<string, TemplateHelper>;
  
  async compileTemplate(templatePath: string): Promise<CompiledTemplate> {
    const source = await this.loadTemplateSource(templatePath);
    const ast = this.parseTemplate(source);
    return this.compileAST(ast);
  }
  
  async renderTemplate(
    templateId: string, 
    context: TemplateContext
  ): Promise<string> {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new TemplateError(`Template not found: ${templateId}`);
    }
    
    // Enrichissement du contexte avec les helpers
    const enrichedContext = {
      ...context,
      helpers: this.helpers,
      partials: this.partials,
      formatters: this.getFormatters()
    };
    
    return template.render(enrichedContext);
  }
  
  registerHelper(name: string, helper: TemplateHelper): void {
    this.helpers.set(name, helper);
  }
}

// Helpers spécialisés pour SDD
class SDDTemplateHelpers {
  static generateTaskId(baseId: string, index: number): string {
    return `${baseId}.${index}`;
  }
  
  static formatUserStory(role: string, want: string, benefit: string): string {
    return `En tant que ${role}, je veux ${want}, afin de ${benefit}`;
  }
  
  static generateAcceptanceCriteria(conditions: Condition[]): string {
    return conditions.map(c => 
      `${c.type.toUpperCase()} ${c.condition} ALORS ${c.action}`
    ).join('\n');
  }
}
```

### Orchestrateur de Prompts Intelligent

#### Génération de Prompts Contextuels
```typescript
class PromptOrchestrator {
  private promptTemplates: Map<string, PromptTemplate>;
  private contextBuilders: Map<SpecPhase, ContextBuilder>;
  
  async generatePrompt(
    phase: SpecPhase,
    projectContext: ProjectContext,
    userInput?: string
  ): Promise<GeneratedPrompt> {
    const builder = this.contextBuilders.get(phase);
    if (!builder) {
      throw new PromptError(`No context builder for phase: ${phase}`);
    }
    
    // Construction du contexte spécialisé
    const context = await builder.buildContext(projectContext, userInput);
    
    // Sélection du template de prompt approprié
    const templateId = this.selectPromptTemplate(phase, context);
    const template = this.promptTemplates.get(templateId);
    
    // Génération du prompt final
    return {
      content: await template.render(context),
      metadata: {
        phase,
        templateId,
        contextSize: this.calculateContextSize(context),
        generatedAt: new Date().toISOString()
      }
    };
  }
}

class RequirementsContextBuilder implements ContextBuilder {
  async buildContext(
    projectContext: ProjectContext, 
    userInput?: string
  ): Promise<PromptContext> {
    return {
      project: {
        name: projectContext.name,
        description: projectContext.description,
        techStack: projectContext.techStack
      },
      phase: 'requirements',
      template: await this.loadTemplate('requirements-template.md'),
      guidelines: await this.getValidationGuidelines('requirements'),
      examples: await this.getExampleRequirements(projectContext.domain),
      constraints: await this.getProjectConstraints(projectContext),
      userInput: userInput || ''
    };
  }
}
```

### Communication MCP Optimisée

#### Gestionnaire de Connexions Avancé
```typescript
class ConnectionManager {
  private connections: Map<string, MCPConnection>;
  private heartbeatInterval: NodeJS.Timeout;
  
  async establishConnection(transport: Transport): Promise<MCPConnection> {
    const connection = new MCPConnection(transport);
    
    // Configuration des handlers d'événements
    connection.on('message', this.handleIncomingMessage.bind(this));
    connection.on('error', this.handleConnectionError.bind(this));
    connection.on('close', this.handleConnectionClose.bind(this));
    
    // Négociation des capacités
    await this.negotiateCapabilities(connection);
    
    // Démarrage du heartbeat
    this.startHeartbeat(connection);
    
    return connection;
  }
  
  private async negotiateCapabilities(connection: MCPConnection): Promise<void> {
    const capabilities: ServerCapabilities = {
      resources: {
        subscribe: true,
        listChanged: true
      },
      tools: {
        listChanged: true
      },
      prompts: {
        listChanged: true
      },
      logging: {},
      experimental: {
        'sdd-workflow': {
          version: '1.0.0',
          features: ['validation', 'templates', 'orchestration']
        }
      }
    };
    
    await connection.sendInitializeResponse({
      protocolVersion: '2024-11-05',
      capabilities,
      serverInfo: {
        name: 'SDD-MCP-Server',
        version: '1.0.0'
      }
    });
  }
}
```

### Monitoring et Observabilité

#### Système de Métriques
```typescript
class MetricsCollector {
  private metrics: Map<string, Metric>;
  private timeseries: TimeSeriesDB;
  
  recordWorkflowMetrics(event: WorkflowEvent): void {
    const metrics = {
      'workflow.phase.duration': {
        value: event.duration,
        tags: { phase: event.phase, project: event.projectId }
      },
      'workflow.validation.success_rate': {
        value: event.validationSuccessful ? 1 : 0,
        tags: { phase: event.phase, validator: event.validatorId }
      },
      'workflow.tasks.completion_rate': {
        value: event.completionRate,
        tags: { project: event.projectId }
      }
    };
    
    Object.entries(metrics).forEach(([name, metric]) => {
      this.timeseries.record(name, metric.value, metric.tags);
    });
  }
  
  async generateHealthReport(): Promise<HealthReport> {
    const now = Date.now();
    const hourAgo = now - (60 * 60 * 1000);
    
    return {
      status: await this.calculateOverallHealth(),
      metrics: {
        activeProjects: await this.countActiveProjects(),
        completedWorkflows: await this.countCompletedWorkflows(hourAgo, now),
        validationSuccessRate: await this.calculateValidationSuccessRate(hourAgo, now),
        averageTaskDuration: await this.calculateAverageTaskDuration(hourAgo, now)
      },
      timestamp: new Date().toISOString()
    };
  }
}
```

## Plan d'Implémentation

### Phase 1 : Foundation (4-6 semaines)
1. **Core MCP Engine** : Implémentation de base du protocole
   - Serveur JSON-RPC 2.0 complet
   - Gestionnaire de connexions avec heartbeat
   - Négociation des capacités MCP
   
2. **State Manager** : Système de persistence robuste
   - Base de données PostgreSQL avec migrations
   - Cache Redis pour les performances
   - Système de versioning et rollback
   
3. **Template Engine** : Moteur de templates avancé
   - Compilateur de templates avec AST
   - Système de partials et helpers
   - Cache intelligent des templates compilés
   
4. **Outils de base** : `sdd_project_init`, `sdd_project_status`
   - Validation des schémas JSON stricte
   - Gestion d'erreurs comprehensive
   - Logging structuré

### Phase 2 : Workflow Core (6-8 semaines)
1. **Workflow Manager** : Gestionnaire de workflow robuste
   - Machine à états pour les phases SDD
   - Gestion des transitions avec validation
   - Support des workflows parallèles
   
2. **Validation Services** : Moteur de validation extensible
   - Architecture basée sur des règles
   - Validation asynchrone avec pool de workers
   - Rapports détaillés avec suggestions
   
3. **Prompt Orchestrator** : Génération intelligente de prompts
   - Templates contextuels dynamiques
   - Optimisation automatique de la taille des prompts
   - A/B testing des prompts
   
4. **Outils de workflow** : Suite complète d'outils MCP
   - Implémentation complète des 8 outils définis
   - Tests d'intégration avec clients MCP
   - Documentation OpenAPI générée

### Phase 3 : Validation & Execution (4-6 semaines)
1. **Outils de validation** : Validation complète multi-phases
   - Règles de validation configurables
   - Validation cross-phase (cohérence)
   - Métriques de qualité automatiques
   
2. **Moteur d'exécution** : Exécution de tâches avancée
   - Exécution parallèle avec dépendances
   - Monitoring en temps réel
   - Rollback automatique en cas d'échec
   
3. **Rapports et métriques** : Observabilité complète
   - Dashboard en temps réel
   - Alertes automatiques
   - Export des métriques (Prometheus)
   
4. **Tests et documentation** : Qualité enterprise
   - Tests unitaires (>90% couverture)
   - Tests d'intégration end-to-end
   - Documentation complète avec exemples

### Phase 4 : Optimisation (2-4 semaines)
1. **Performance** : Optimisations avancées
   - Profiling et optimisation des bottlenecks
   - Cache intelligent multi-niveaux
   - Compression des réponses MCP
   
2. **Ergonomie** : Expérience utilisateur premium
   - Interface web de monitoring
   - CLI pour administration
   - Extensions pour éditeurs populaires
   
3. **Monitoring** : Observabilité production
   - Tracing distribué avec Jaeger
   - Logs structurés avec ELK stack
   - Métriques business avec Grafana
   
4. **Documentation** : Documentation enterprise
   - Guide d'architecture détaillé
   - Tutoriels hands-on
   - API reference complète

### API MCP Détaillée

#### Schéma Complet des Outils MCP

##### 1. Gestion de Projet

###### sdd_project_init
```json
{
  "name": "sdd_project_init",
  "description": "Initialise un nouveau projet SDD avec la structure de base et les documents de pilotage",
  "inputSchema": {
    "type": "object",
    "properties": {
      "project_name": {
        "type": "string",
        "description": "Nom du projet (kebab-case recommandé)",
        "pattern": "^[a-z0-9-]+$",
        "minLength": 3,
        "maxLength": 50
      },
      "description": {
        "type": "string",
        "description": "Description détaillée du projet",
        "minLength": 10,
        "maxLength": 500
      },
      "tech_stack": {
        "type": "array",
        "description": "Technologies utilisées dans le projet",
        "items": {
          "type": "string",
          "enum": ["typescript", "react", "node", "python", "java", "go", "rust", "vue", "angular", "svelte"]
        },
        "minItems": 1,
        "maxItems": 10
      },
      "project_type": {
        "type": "string",
        "description": "Type de projet",
        "enum": ["web_app", "mobile_app", "api", "cli_tool", "library", "desktop_app"],
        "default": "web_app"
      },
      "team_size": {
        "type": "integer",
        "description": "Taille de l'équipe de développement",
        "minimum": 1,
        "maximum": 50,
        "default": 1
      }
    },
    "required": ["project_name", "description", "tech_stack"]
  }
}
```

**Exemple d'appel** :
```json
{
  "jsonrpc": "2.0",
  "id": "1",
  "method": "tools/call",
  "params": {
    "name": "sdd_project_init",
    "arguments": {
      "project_name": "e-commerce-platform",
      "description": "Plateforme e-commerce moderne avec gestion des utilisateurs, produits et commandes",
      "tech_stack": ["typescript", "react", "node"],
      "project_type": "web_app",
      "team_size": 3
    }
  }
}
```

**Réponse** :
```json
{
  "jsonrpc": "2.0",
  "id": "1",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "✅ Projet 'e-commerce-platform' initialisé avec succès\n\n📁 Structure créée:\n- .sdd/project/\n  - product.md\n  - tech.md\n  - structure.md\n- .sdd/specs/ (prêt pour les spécifications)\n\n🔧 Configuration:\n- Type: Web Application\n- Technologies: TypeScript, React, Node.js\n- Équipe: 3 développeurs\n\nProchain pas: Utilisez 'sdd_spec_create' pour créer votre première spécification."
      }
    ],
    "isError": false,
    "metadata": {
      "project_id": "proj_67890abcdef",
      "created_at": "2024-01-15T10:30:00Z",
      "structure_path": ".sdd",
      "estimated_setup_time": "2 minutes"
    }
  }
}
```

###### sdd_project_status
```json
{
  "name": "sdd_project_status",
  "description": "Affiche l'état détaillé du projet SDD avec métriques et progression",
  "inputSchema": {
    "type": "object",
    "properties": {
      "project_id": {
        "type": "string",
        "description": "Identifiant unique du projet",
        "pattern": "^proj_[a-z0-9]{11}$"
      },
      "include_metrics": {
        "type": "boolean",
        "description": "Inclure les métriques détaillées",
        "default": true
      },
      "include_history": {
        "type": "boolean",
        "description": "Inclure l'historique des modifications",
        "default": false
      },
      "format": {
        "type": "string",
        "description": "Format de sortie",
        "enum": ["summary", "detailed", "json"],
        "default": "detailed"
      }
    },
    "required": ["project_id"]
  }
}
```

##### 2. Gestion des Spécifications

###### sdd_spec_create
```json
{
  "name": "sdd_spec_create",
  "description": "Démarre la création d'une nouvelle spécification SDD avec guidance interactive",
  "inputSchema": {
    "type": "object",
    "properties": {
      "project_id": {
        "type": "string",
        "description": "ID du projet parent",
        "pattern": "^proj_[a-z0-9]{11}$"
      },
      "feature_name": {
        "type": "string",
        "description": "Nom de la fonctionnalité (kebab-case)",
        "pattern": "^[a-z0-9-]+$",
        "minLength": 3,
        "maxLength": 50
      },
      "initial_description": {
        "type": "string",
        "description": "Description initiale de la fonctionnalité",
        "minLength": 20,
        "maxLength": 1000
      },
      "priority": {
        "type": "string",
        "description": "Priorité de la fonctionnalité",
        "enum": ["low", "medium", "high", "critical"],
        "default": "medium"
      },
      "estimated_complexity": {
        "type": "string",
        "description": "Complexité estimée",
        "enum": ["simple", "medium", "complex", "epic"],
        "default": "medium"
      },
      "target_users": {
        "type": "array",
        "description": "Utilisateurs cibles",
        "items": {
          "type": "string"
        },
        "minItems": 1
      }
    },
    "required": ["project_id", "feature_name", "initial_description", "target_users"]
  }
}
```

**Exemple de réponse détaillée** :
```json
{
  "jsonrpc": "2.0",
  "id": "2",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "🚀 Spécification 'user-authentication' créée\n\n📋 Détails:\n- ID: spec_12345abcdef\n- Phase actuelle: Requirements (1/4)\n- Priorité: High\n- Complexité estimée: Medium\n- Utilisateurs cibles: end-users, administrators\n\n📝 Prochaines étapes:\n1. Créer le document d'exigences\n2. Valider avec les parties prenantes\n3. Passer à la conception technique\n\n💡 Conseil: Utilisez le prompt généré ci-dessous pour créer vos exigences:"
      },
      {
        "type": "text",
        "text": "# PROMPT POUR EXIGENCES - user-authentication\n\n## CONTEXTE PROJET\nProjet: e-commerce-platform\nType: Web Application\nTech Stack: TypeScript, React, Node.js\n\n## FONCTIONNALITÉ\nNom: user-authentication\nDescription: Système d'authentification sécurisé permettant aux utilisateurs de s'inscrire, se connecter et gérer leur profil\nUtilisateurs cibles: end-users, administrators\n\n## INSTRUCTIONS\nCréez un document d'exigences détaillé suivant le template requirements-template.md.\n\n### STRUCTURE REQUISE\n1. **Introduction** avec alignement à la vision produit\n2. **Exigences fonctionnelles** avec user stories au format:\n   \"En tant que [rôle], je veux [fonctionnalité], afin de [bénéfice]\"\n3. **Critères d'acceptation** au format EARS:\n   \"QUAND [condition] ALORS [système] DOIT [action]\"\n4. **Exigences non fonctionnelles** (sécurité, performance, utilisabilité)\n\n### CRITÈRES DE QUALITÉ\n- User stories spécifiques et mesurables\n- Critères d'acceptation testables\n- Couverture de tous les scénarios (nominal + erreur)\n- Alignement avec les standards de sécurité\n\n### VALIDATION AUTOMATIQUE\nVotre document sera validé selon :\n- ✅ Format des user stories\n- ✅ Complétude des critères d'acceptation\n- ✅ Cohérence avec la vision produit\n- ✅ Standards de sécurité pour l'authentification"
      }
    ],
    "isError": false,
    "metadata": {
      "spec_id": "spec_12345abcdef",
      "current_phase": "requirements",
      "next_tool": "sdd_validate_requirements",
      "estimated_duration": "2-4 hours",
      "template_used": "requirements-template.md"
    }
  }
}
```

##### 3. Validation Multi-Phase

###### sdd_validate_requirements
```json
{
  "name": "sdd_validate_requirements",
  "description": "Valide un document d'exigences selon les critères de qualité SDD",
  "inputSchema": {
    "type": "object",
    "properties": {
      "spec_id": {
        "type": "string",
        "description": "ID de la spécification",
        "pattern": "^spec_[a-z0-9]{11}$"
      },
      "content": {
        "type": "string",
        "description": "Contenu du document d'exigences en Markdown",
        "minLength": 100
      },
      "validation_level": {
        "type": "string",
        "description": "Niveau de validation",
        "enum": ["basic", "standard", "strict"],
        "default": "standard"
      },
      "auto_fix": {
        "type": "boolean",
        "description": "Tentative de correction automatique des problèmes mineurs",
        "default": false
      }
    },
    "required": ["spec_id", "content"]
  }
}
```

**Exemple de validation avec erreurs** :
```json
{
  "jsonrpc": "2.0",
  "id": "3",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "🔍 RAPPORT DE VALIDATION - Exigences\n\n❌ **VALIDATION ÉCHOUÉE** (3 erreurs, 2 avertissements)\n\n## 🚨 Erreurs Critiques\n\n### 1. Format User Story Incorrect\n**Ligne 45**: \"L'utilisateur veut se connecter\"\n**Problème**: Ne suit pas le format \"En tant que [rôle], je veux [fonctionnalité], afin de [bénéfice]\"\n**Correction suggérée**: \"En tant qu'utilisateur enregistré, je veux me connecter à mon compte, afin d'accéder à mes informations personnelles\"\n\n### 2. Critères d'Acceptation Manquants\n**Section**: Exigence 2.3 - Réinitialisation mot de passe\n**Problème**: Aucun critère d'acceptation défini\n**Action requise**: Ajouter des critères EARS (QUAND/SI/ALORS)\n\n### 3. Exigences Non Fonctionnelles Incomplètes\n**Section**: Sécurité\n**Problème**: Pas de spécification pour le chiffrement des mots de passe\n**Action requise**: Définir l'algorithme de hachage (ex: bcrypt)\n\n## ⚠️ Avertissements\n\n### 1. User Story Trop Vague\n**Ligne 67**: \"En tant qu'admin, je veux gérer les utilisateurs, afin d'administrer le système\"\n**Recommandation**: Être plus spécifique sur les actions d'administration\n\n### 2. Critère d'Acceptation Flou\n**Ligne 89**: \"QUAND l'utilisateur saisit un mot de passe ALORS il doit être sécurisé\"\n**Recommandation**: Définir des critères mesurables (longueur, complexité)\n\n## 📊 Métriques de Qualité\n\n- **User Stories**: 12 total, 8 valides (67%)\n- **Critères d'Acceptation**: 28 total, 23 valides (82%)\n- **Couverture Fonctionnelle**: 85%\n- **Couverture Non Fonctionnelle**: 60%\n\n## 🔧 Actions Recommandées\n\n1. **Corriger les 3 erreurs critiques** avant validation\n2. **Améliorer les user stories vagues** pour plus de clarté\n3. **Compléter les exigences de sécurité** selon les standards\n4. **Ajouter des cas d'erreur** dans les critères d'acceptation\n\n## 📚 Ressources\n\n- [Guide des User Stories](./docs/user-stories-guide.md)\n- [Template EARS](./docs/ears-template.md)\n- [Standards de Sécurité](./docs/security-requirements.md)"
      }
    ],
    "isError": false,
    "metadata": {
      "validation_status": "failed",
      "score": 67,
      "errors": 3,
      "warnings": 2,
      "suggestions": 4,
      "estimated_fix_time": "45 minutes",
      "can_auto_fix": true,
      "next_action": "fix_errors_and_revalidate"
    }
  }
}
```

##### 4. Exécution de Tâches

###### sdd_execute_task
```json
{
  "name": "sdd_execute_task",
  "description": "Exécute une tâche spécifique avec monitoring en temps réel",
  "inputSchema": {
    "type": "object",
    "properties": {
      "spec_id": {
        "type": "string",
        "description": "ID de la spécification",
        "pattern": "^spec_[a-z0-9]{11}$"
      },
      "task_id": {
        "type": "string",
        "description": "ID ou numéro de la tâche (ex: '1.2.3')",
        "pattern": "^[0-9]+(\\.[0-9]+)*$"
      },
      "execution_mode": {
        "type": "string",
        "description": "Mode d'exécution",
        "enum": ["interactive", "automated", "simulation"],
        "default": "interactive"
      },
      "context": {
        "type": "object",
        "description": "Contexte d'exécution supplémentaire",
        "properties": {
          "working_directory": {
            "type": "string",
            "description": "Répertoire de travail"
          },
          "environment": {
            "type": "string",
            "enum": ["development", "staging", "production"],
            "default": "development"
          },
          "dry_run": {
            "type": "boolean",
            "description": "Simulation sans modifications réelles",
            "default": false
          }
        }
      },
      "timeout": {
        "type": "integer",
        "description": "Timeout en secondes",
        "minimum": 30,
        "maximum": 3600,
        "default": 600
      }
    },
    "required": ["spec_id", "task_id"]
  }
}
```

**Exemple d'exécution avec monitoring** :
```json
{
  "jsonrpc": "2.0",
  "id": "4",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "🔄 EXÉCUTION TÂCHE 2.1 - Créer le modèle User\n\n📋 **Détails de la tâche:**\n- Spécification: user-authentication (spec_12345abcdef)\n- Tâche: 2.1 - Créer le modèle User dans src/models/User.ts\n- Mode: Interactive\n- Environnement: Development\n\n⏱️ **Progression:**\n```\n[████████████████████████████████████████] 100%\n\n✅ 10:32:15 - Analyse du contexte projet\n✅ 10:32:18 - Chargement du template de modèle\n✅ 10:32:22 - Génération du code TypeScript\n✅ 10:32:25 - Validation syntaxique\n✅ 10:32:28 - Création du fichier src/models/User.ts\n✅ 10:32:30 - Exécution des tests unitaires\n✅ 10:32:35 - Mise à jour de l'index des exports\n✅ 10:32:37 - Commit automatique\n```\n\n📁 **Fichiers créés/modifiés:**\n- ✅ `src/models/User.ts` (nouveau)\n- ✅ `src/models/index.ts` (modifié)\n- ✅ `tests/models/User.test.ts` (nouveau)\n\n🧪 **Tests:**\n- ✅ 5/5 tests unitaires passés\n- ✅ Couverture: 95%\n- ✅ Aucune régression détectée\n\n📊 **Métriques:**\n- Durée: 22 secondes\n- Lignes de code: 127\n- Complexité cyclomatique: 3\n- Score qualité: A+\n\n🎉 **Tâche terminée avec succès!**\n\nProchaine tâche suggérée: 2.2 - Ajouter les méthodes de validation au modèle User"
      },
      {
        "type": "text",
        "text": "📝 **Code généré - src/models/User.ts:**\n\n```typescript\nimport { BaseModel } from './BaseModel';\nimport { validateEmail, validatePassword } from '../utils/validation';\nimport { UserRole } from '../types/auth';\n\nexport interface UserAttributes {\n  id: string;\n  email: string;\n  passwordHash: string;\n  firstName: string;\n  lastName: string;\n  role: UserRole;\n  isActive: boolean;\n  emailVerified: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport class User extends BaseModel<UserAttributes> {\n  static tableName = 'users';\n  \n  constructor(attributes: Partial<UserAttributes>) {\n    super(attributes);\n  }\n  \n  get fullName(): string {\n    return `${this.firstName} ${this.lastName}`.trim();\n  }\n  \n  validateEmail(): boolean {\n    return validateEmail(this.email);\n  }\n  \n  async setPassword(plainPassword: string): Promise<void> {\n    if (!validatePassword(plainPassword)) {\n      throw new Error('Password does not meet security requirements');\n    }\n    \n    this.passwordHash = await this.hashPassword(plainPassword);\n  }\n  \n  async verifyPassword(plainPassword: string): Promise<boolean> {\n    return await this.comparePassword(plainPassword, this.passwordHash);\n  }\n  \n  isAdmin(): boolean {\n    return this.role === UserRole.ADMIN;\n  }\n  \n  private async hashPassword(password: string): Promise<string> {\n    const bcrypt = await import('bcryptjs');\n    return bcrypt.hash(password, 12);\n  }\n  \n  private async comparePassword(password: string, hash: string): Promise<boolean> {\n    const bcrypt = await import('bcryptjs');\n    return bcrypt.compare(password, hash);\n  }\n}\n```"
      }
    ],
    "isError": false,
    "metadata": {
      "execution_id": "exec_abc123def456",
      "task_status": "completed",
      "duration_seconds": 22,
      "files_created": 2,
      "files_modified": 1,
      "tests_passed": 5,
      "coverage_percentage": 95,
      "next_task_id": "2.2",
      "commit_hash": "a1b2c3d4e5f6",
      "quality_score": "A+"
    }
  }
}
```

#### Ressources MCP Exposées

##### 1. Templates et Documentation

###### Ressource: Templates
```json
{
  "uri": "sdd://templates/{template_name}",
  "name": "SDD Templates",
  "description": "Templates de spécification SDD",
  "mimeType": "text/markdown"
}
```

**Exemple d'accès** :
```
URI: sdd://templates/requirements-template.md
URI: sdd://templates/design-template.md
URI: sdd://templates/tasks-template.md
URI: sdd://templates/tech-template.md
URI: sdd://templates/structure-template.md
URI: sdd://templates/product-template.md
```

##### 2. État des Projets

###### Ressource: Project State
```json
{
  "uri": "sdd://projects/{project_id}/state",
  "name": "Project State",
  "description": "État complet du projet SDD",
  "mimeType": "application/json"
}
```

##### 3. Métriques et Rapports

###### Ressource: Analytics
```json
{
  "uri": "sdd://analytics/{project_id}?period={period}&metrics={metrics}",
  "name": "Project Analytics",
  "description": "Métriques et analyses du projet",
  "mimeType": "application/json"
}
```

#### Prompts MCP Spécialisés

##### 1. Prompt Requirements Generation
```json
{
  "name": "generate_requirements",
  "description": "Génère un prompt contextualisé pour la création d'exigences",
  "arguments": [
    {
      "name": "project_context",
      "description": "Contexte du projet (nom, description, tech stack)",
      "required": true
    },
    {
      "name": "feature_description",
      "description": "Description de la fonctionnalité à spécifier",
      "required": true
    },
    {
      "name": "target_users",
      "description": "Utilisateurs cibles de la fonctionnalité",
      "required": true
    },
    {
      "name": "complexity_level",
      "description": "Niveau de complexité (simple, medium, complex)",
      "required": false
    }
  ]
}
```

##### 2. Prompt Design Creation
```json
{
  "name": "generate_design",
  "description": "Génère un prompt pour la conception technique",
  "arguments": [
    {
      "name": "requirements_summary",
      "description": "Résumé des exigences validées",
      "required": true
    },
    {
      "name": "tech_constraints",
      "description": "Contraintes techniques du projet",
      "required": true
    },
    {
      "name": "existing_architecture",
      "description": "Architecture existante à prendre en compte",
      "required": false
    }
  ]
}
```

#### Messages de Notification MCP

##### 1. Notifications de Changement d'État
```json
{
  "jsonrpc": "2.0",
  "method": "notifications/resources/updated",
  "params": {
    "uri": "sdd://projects/proj_67890abcdef/state",
    "changes": [
      {
        "type": "phase_transition",
        "spec_id": "spec_12345abcdef",
        "from_phase": "requirements",
        "to_phase": "design",
        "timestamp": "2024-01-15T11:45:00Z"
      }
    ]
  }
}
```

##### 2. Notifications de Validation
```json
{
  "jsonrpc": "2.0",
  "method": "notifications/validation/completed",
  "params": {
    "spec_id": "spec_12345abcdef",
    "phase": "requirements",
    "status": "passed",
    "score": 87,
    "issues_resolved": 3,
    "timestamp": "2024-01-15T11:30:00Z"
  }
}
```

#### Gestion d'Erreurs Avancée

##### Codes d'Erreur Spécialisés
```typescript
enum SDDErrorCodes {
  // Erreurs de projet
  PROJECT_NOT_FOUND = 1001,
  PROJECT_ALREADY_EXISTS = 1002,
  PROJECT_INVALID_CONFIG = 1003,
  
  // Erreurs de spécification
  SPEC_NOT_FOUND = 2001,
  SPEC_INVALID_PHASE = 2002,
  SPEC_VALIDATION_FAILED = 2003,
  SPEC_TRANSITION_BLOCKED = 2004,
  
  // Erreurs de tâche
  TASK_NOT_FOUND = 3001,
  TASK_DEPENDENCY_UNMET = 3002,
  TASK_EXECUTION_FAILED = 3003,
  TASK_TIMEOUT = 3004,
  
  // Erreurs de validation
  VALIDATION_RULE_FAILED = 4001,
  VALIDATION_TIMEOUT = 4002,
  VALIDATION_SCHEMA_ERROR = 4003,
  
  // Erreurs système
  TEMPLATE_NOT_FOUND = 5001,
  STORAGE_ERROR = 5002,
  CACHE_ERROR = 5003,
  NETWORK_ERROR = 5004
}
```

##### Exemple de Réponse d'Erreur Détaillée
```json
{
  "jsonrpc": "2.0",
  "id": "5",
  "error": {
    "code": 2003,
    "message": "Validation de spécification échouée",
    "data": {
      "error_type": "SPEC_VALIDATION_FAILED",
      "spec_id": "spec_12345abcdef",
      "phase": "requirements",
      "validation_errors": [
        {
          "rule_id": "req-user-story-format",
          "line": 45,
          "severity": "error",
          "message": "Format de user story incorrect",
          "suggestion": "Utilisez le format: 'En tant que [rôle], je veux [fonctionnalité], afin de [bénéfice]'"
        }
      ],
      "recovery_actions": [
        "Corrigez les erreurs de format des user stories",
        "Relancez la validation avec sdd_validate_requirements",
        "Consultez le guide des user stories pour plus d'aide"
      ],
      "help_url": "https://docs.sdd-mcp.com/validation/requirements",
      "estimated_fix_time": "15 minutes"
    }
  }
}
```

### Configuration et Déploiement

#### Configuration du Serveur
```yaml
# config/server.yaml
server:
  name: "SDD-MCP-Server"
  version: "1.0.0"
  host: "localhost"
  port: 3000
  
mcp:
  protocol_version: "2024-11-05"
  transports:
    - type: "stdio"
      enabled: true
    - type: "websocket"
      enabled: true
      port: 3001
      cors:
        origins: ["*"]
    - type: "http"
      enabled: false
      
database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "sdd_mcp"
  username: "${DB_USERNAME}"
  password: "${DB_PASSWORD}"
  pool:
    min: 5
    max: 20
    acquire_timeout: 30000
    
cache:
  type: "redis"
  host: "localhost"
  port: 6379
  ttl: 3600
  
logging:
  level: "info"
  format: "json"
  outputs:
    - type: "console"
    - type: "file"
      path: "logs/sdd-mcp.log"
      max_size: "100MB"
      max_files: 10
      
validation:
  timeout: 30000
  max_parallel: 5
  rules_path: "config/validation-rules"
  
templates:
  path: "templates"
  cache_enabled: true
  watch_changes: true
```

#### Docker Deployment
```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime

RUN addgroup -g 1001 -S sddmcp && \
    adduser -S sddmcp -u 1001

WORKDIR /app

# Installation des dépendances système
RUN apk add --no-cache \
    postgresql-client \
    redis \
    curl

# Copie des fichiers de l'application
COPY --from=builder /app/node_modules ./node_modules
COPY --chown=sddmcp:sddmcp . .

USER sddmcp

EXPOSE 3000 3001

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  sdd-mcp-server:
    build: .
    ports:
      - "3000:3000"
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DB_USERNAME=sdduser
      - DB_PASSWORD=${DB_PASSWORD}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./config:/app/config:ro
      - ./templates:/app/templates:ro
      - logs:/app/logs
    restart: unless-stopped
    
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=sdd_mcp
      - POSTGRES_USER=sdduser
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d:ro
    restart: unless-stopped
    
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    restart: unless-stopped
    
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3003:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  grafana_data:
  logs:
```

### Sécurité et Authentification

#### Stratégie de Sécurité
```typescript
class SecurityManager {
  private tokenValidator: TokenValidator;
  private rateLimiter: RateLimiter;
  private auditLogger: AuditLogger;
  
  async authenticateConnection(connectionId: string, credentials: any): Promise<AuthResult> {
    // Validation du token JWT
    const tokenResult = await this.tokenValidator.validate(credentials.token);
    if (!tokenResult.valid) {
      await this.auditLogger.logFailedAuth(connectionId, 'invalid_token');
      throw new AuthenticationError('Invalid token');
    }
    
    // Vérification des permissions
    const permissions = await this.getPermissions(tokenResult.userId);
    
    // Application du rate limiting
    await this.rateLimiter.checkLimits(connectionId, tokenResult.userId);
    
    return {
      userId: tokenResult.userId,
      permissions,
      sessionId: generateSessionId()
    };
  }
  
  async authorizeOperation(
    sessionId: string, 
    operation: string, 
    resource: string
  ): Promise<boolean> {
    const session = await this.getSession(sessionId);
    return this.checkPermission(session.permissions, operation, resource);
  }
}

// Configuration de sécurité
interface SecurityConfig {
  jwt: {
    secret: string;
    expiresIn: string;
    algorithm: 'HS256' | 'RS256';
  };
  rateLimiting: {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests: boolean;
  };
  cors: {
    origins: string[];
    methods: string[];
    allowedHeaders: string[];
  };
  encryption: {
    algorithm: string;
    keyDerivation: {
      iterations: number;
      keyLength: number;
      digest: string;
    };
  };
}
```

### Tests et Qualité

#### Architecture de Tests
```typescript
// Tests unitaires avec Jest
describe('WorkflowManager', () => {
  let workflowManager: WorkflowManager;
  let mockStateManager: jest.Mocked<StateManager>;
  let mockValidationService: jest.Mocked<ValidationService>;
  
  beforeEach(() => {
    mockStateManager = createMockStateManager();
    mockValidationService = createMockValidationService();
    workflowManager = new WorkflowManager(mockStateManager, mockValidationService);
  });
  
  describe('transitionToNextPhase', () => {
    it('should transition from requirements to design when validation passes', async () => {
      // Arrange
      const specId = 'test-spec-123';
      const currentState = createMockSpecState(SpecPhase.Requirements);
      mockStateManager.getSpecState.mockResolvedValue(currentState);
      mockValidationService.validatePhase.mockResolvedValue({ valid: true, issues: [] });
      
      // Act
      const result = await workflowManager.transitionToNextPhase(specId);
      
      // Assert
      expect(result.newPhase).toBe(SpecPhase.Design);
      expect(mockStateManager.updateSpecPhase).toHaveBeenCalledWith(specId, SpecPhase.Design);
    });
    
    it('should throw error when validation fails', async () => {
      // Arrange
      const specId = 'test-spec-123';
      const currentState = createMockSpecState(SpecPhase.Requirements);
      mockStateManager.getSpecState.mockResolvedValue(currentState);
      mockValidationService.validatePhase.mockResolvedValue({ 
        valid: false, 
        issues: [{ severity: 'error', message: 'Invalid format' }] 
      });
      
      // Act & Assert
      await expect(workflowManager.transitionToNextPhase(specId))
        .rejects.toThrow(ValidationError);
    });
  });
});

// Tests d'intégration avec TestContainers
describe('SDD MCP Server Integration', () => {
  let server: MCPServer;
  let postgres: StartedPostgreSqlContainer;
  let redis: StartedRedisContainer;
  
  beforeAll(async () => {
    // Démarrage des conteneurs de test
    postgres = await new PostgreSqlContainer()
      .withDatabase('test_sdd_mcp')
      .withUsername('test')
      .withPassword('test')
      .start();
      
    redis = await new RedisContainer().start();
    
    // Configuration du serveur de test
    const config = {
      database: {
        host: postgres.getHost(),
        port: postgres.getMappedPort(5432),
        database: 'test_sdd_mcp',
        username: 'test',
        password: 'test'
      },
      cache: {
        host: redis.getHost(),
        port: redis.getMappedPort(6379)
      }
    };
    
    server = new MCPServer(config);
    await server.start();
  });
  
  afterAll(async () => {
    await server.stop();
    await postgres.stop();
    await redis.stop();
  });
  
  it('should complete full SDD workflow', async () => {
    const client = new MCPTestClient(server.getConnectionString());
    
    // Test du workflow complet
    const project = await client.call('sdd_project_init', {
      project_name: 'test-project',
      description: 'Integration test project'
    });
    
    const spec = await client.call('sdd_spec_create', {
      feature_name: 'user-auth',
      initial_description: 'User authentication system'
    });
    
    // Validation que le workflow suit les bonnes étapes
    expect(spec.current_phase).toBe('requirements');
    
    // Continue avec les autres phases...
  });
});
```

### Performance et Optimisation

#### Stratégies de Performance
```typescript
class PerformanceOptimizer {
  private connectionPool: ConnectionPool;
  private queryCache: QueryCache;
  private responseCompressor: ResponseCompressor;
  
  async optimizeQuery(query: DatabaseQuery): Promise<OptimizedQuery> {
    // Analyse de la requête
    const queryPlan = await this.analyzeQuery(query);
    
    // Application des optimisations
    if (queryPlan.canUseIndex) {
      query.addIndexHint(queryPlan.suggestedIndex);
    }
    
    if (queryPlan.canBeBatched) {
      return this.batchQuery(query);
    }
    
    return query;
  }
  
  async cacheResponse(
    key: string, 
    response: any, 
    ttl: number = 3600
  ): Promise<void> {
    // Compression avant mise en cache
    const compressed = await this.responseCompressor.compress(response);
    
    // Stockage avec métadonnées
    await this.queryCache.set(key, {
      data: compressed,
      compressed: true,
      timestamp: Date.now(),
      ttl
    });
  }
  
  async getFromCache(key: string): Promise<any | null> {
    const cached = await this.queryCache.get(key);
    if (!cached) return null;
    
    // Vérification de l'expiration
    if (Date.now() - cached.timestamp > cached.ttl * 1000) {
      await this.queryCache.delete(key);
      return null;
    }
    
    // Décompression si nécessaire
    if (cached.compressed) {
      return await this.responseCompressor.decompress(cached.data);
    }
    
    return cached.data;
  }
}
```

### Monitoring Avancé

#### Métriques et Alertes
```typescript
class MonitoringSystem {
  private metricsRegistry: MetricsRegistry;
  private alertManager: AlertManager;
  
  setupMetrics(): void {
    // Métriques de performance
    this.metricsRegistry.registerGauge('sdd_active_connections', 'Number of active MCP connections');
    this.metricsRegistry.registerCounter('sdd_requests_total', 'Total number of MCP requests');
    this.metricsRegistry.registerHistogram('sdd_request_duration', 'Request duration in milliseconds');
    
    // Métriques métier
    this.metricsRegistry.registerGauge('sdd_active_projects', 'Number of active SDD projects');
    this.metricsRegistry.registerCounter('sdd_validations_total', 'Total number of validations performed');
    this.metricsRegistry.registerGauge('sdd_validation_success_rate', 'Validation success rate');
    
    // Métriques système
    this.metricsRegistry.registerGauge('sdd_database_connections', 'Active database connections');
    this.metricsRegistry.registerGauge('sdd_cache_hit_rate', 'Cache hit rate percentage');
  }
  
  setupAlerting(): void {
    // Alertes de performance
    this.alertManager.addAlert({
      name: 'HighResponseTime',
      condition: 'sdd_request_duration > 5000',
      severity: 'warning',
      message: 'Request response time is above 5 seconds'
    });
    
    // Alertes métier
    this.alertManager.addAlert({
      name: 'ValidationFailureSpike',
      condition: 'sdd_validation_success_rate < 0.8',
      severity: 'critical',
      message: 'Validation success rate has dropped below 80%'
    });
    
    // Alertes système
    this.alertManager.addAlert({
      name: 'DatabaseConnectionExhaustion',
      condition: 'sdd_database_connections > 18',
      severity: 'critical',
      message: 'Database connection pool is nearly exhausted'
    });
  }
}
```

## Exemple d'Utilisation Bout-en-Bout

### Scénario : Développement d'une Fonctionnalité d'Authentification

Cet exemple illustre un workflow complet de développement SDD via le serveur MCP, depuis l'initialisation du projet jusqu'à l'exécution des tâches.

#### Étape 1 : Initialisation du Projet

**Contexte** : Une équipe souhaite développer une plateforme e-commerce moderne.

**Appel MCP** :
```json
{
  "jsonrpc": "2.0",
  "id": "1",
  "method": "tools/call",
  "params": {
    "name": "sdd_project_init",
    "arguments": {
      "project_name": "e-commerce-platform",
      "description": "Plateforme e-commerce avec gestion utilisateurs, produits et commandes. Interface moderne en React avec backend Node.js.",
      "tech_stack": ["typescript", "react", "node"],
      "project_type": "web_app",
      "team_size": 3
    }
  }
}
```

**Réponse du Serveur** :
```json
{
  "jsonrpc": "2.0",
  "id": "1",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "✅ Projet 'e-commerce-platform' initialisé avec succès\n\n📁 Structure créée:\n- .sdd/project/\n  - product.md (Vision produit)\n  - tech.md (Stack technique)\n  - structure.md (Architecture)\n- .sdd/specs/ (Spécifications)\n- .sdd/templates/ (Templates)\n\n🎯 Vision produit configurée:\n- Plateforme B2C moderne\n- Performance et UX prioritaires\n- Sécurité renforcée\n- Scalabilité horizontale\n\n🔧 Stack technique validé:\n- Frontend: React 18 + TypeScript\n- Backend: Node.js + Express\n- Base de données: PostgreSQL\n- Authentication: JWT + OAuth2\n\nProchain pas: Créez votre première spécification avec 'sdd_spec_create'"
      }
    ],
    "isError": false,
    "metadata": {
      "project_id": "proj_67890abcdef",
      "created_at": "2024-01-15T10:30:00Z",
      "structure_path": ".sdd"
    }
  }
}
```

#### Étape 2 : Création de la Spécification d'Authentification

**Appel MCP** :
```json
{
  "jsonrpc": "2.0",
  "id": "2",
  "method": "tools/call",
  "params": {
    "name": "sdd_spec_create",
    "arguments": {
      "project_id": "proj_67890abcdef",
      "feature_name": "user-authentication",
      "initial_description": "Système d'authentification sécurisé permettant aux utilisateurs de s'inscrire, se connecter, gérer leur profil et réinitialiser leur mot de passe. Support OAuth2 pour les connexions sociales.",
      "priority": "high",
      "estimated_complexity": "medium",
      "target_users": ["end-users", "administrators", "guest-users"]
    }
  }
}
```

**Réponse avec Prompt Généré** :
```json
{
  "jsonrpc": "2.0",
  "id": "2",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "🚀 Spécification 'user-authentication' créée avec succès\n\n📋 Détails:\n- ID: spec_12345abcdef\n- Phase: Requirements (1/4)\n- Priorité: High\n- Complexité: Medium\n- Utilisateurs: end-users, administrators, guest-users\n\n🎯 Contexte métier:\n- Fonctionnalité critique pour la plateforme\n- Exigences de sécurité élevées (e-commerce)\n- Support multi-dispositifs requis\n- Conformité RGPD nécessaire\n\n📝 Prompt généré pour la phase Requirements:"
      },
      {
        "type": "text",
        "text": "---\n\n# CRÉATION D'EXIGENCES - user-authentication\n\n## CONTEXTE PROJET\n\n**Projet**: e-commerce-platform  \n**Type**: Web Application  \n**Technologies**: TypeScript, React, Node.js  \n**Équipe**: 3 développeurs  \n\n**Vision produit**: Plateforme e-commerce moderne privilégiant performance et sécurité\n\n## FONCTIONNALITÉ À SPÉCIFIER\n\n**Nom**: user-authentication  \n**Description**: Système d'authentification sécurisé avec inscription, connexion, gestion de profil et OAuth2  \n**Priorité**: High (fonctionnalité critique)  \n**Utilisateurs cibles**: end-users, administrators, guest-users  \n\n## INSTRUCTIONS DE CRÉATION\n\nCréez un document d'exigences détaillé suivant la structure requirements-template.md.\n\n### STRUCTURE OBLIGATOIRE\n\n1. **Introduction et Vision**\n   - Alignement avec les objectifs de la plateforme e-commerce\n   - Enjeux de sécurité et d'expérience utilisateur\n\n2. **Exigences Fonctionnelles** (format user stories)\n   ```\n   En tant que [rôle précis], \n   je veux [action/fonctionnalité spécifique], \n   afin de [bénéfice métier mesurable]\n   ```\n\n3. **Critères d'Acceptation** (format EARS)\n   ```\n   QUAND [condition précise]\n   ALORS le système DOIT [action vérifiable]\n   ET [contrainte supplémentaire si nécessaire]\n   ```\n\n4. **Exigences Non Fonctionnelles**\n   - Sécurité (chiffrement, sessions, attaques)\n   - Performance (temps de réponse, concurrence)\n   - Utilisabilité (accessibilité, responsive)\n   - Conformité (RGPD, standards web)\n\n### EXIGENCES SPÉCIFIQUES\n\n**Sécurité** (critiques pour e-commerce):\n- Chiffrement des mots de passe (bcrypt minimum)\n- Protection contre brute force\n- Sessions sécurisées avec timeout\n- Validation OWASP Top 10\n\n**Fonctionnalités core** à couvrir:\n- Inscription avec validation email\n- Connexion/déconnexion\n- Réinitialisation mot de passe\n- Gestion profil utilisateur\n- OAuth2 (Google, Facebook)\n- Administration des comptes\n\n**Utilisateurs types** à considérer:\n- Acheteurs occasionnels (simplicité)\n- Clients réguliers (rapidité)\n- Administrateurs (contrôle complet)\n- Invités (conversion facile)\n\n### CRITÈRES DE QUALITÉ\n\n✅ **Complétude**: Tous les scénarios utilisateur couverts  \n✅ **Précision**: User stories spécifiques et mesurables  \n✅ **Testabilité**: Critères d'acceptation vérifiables  \n✅ **Cohérence**: Alignement avec la vision produit  \n✅ **Sécurité**: Standards e-commerce respectés  \n\n### VALIDATION AUTOMATIQUE\n\nVotre document sera automatiquement validé selon:\n- Format des user stories\n- Exhaustivité des critères d'acceptation\n- Couverture des exigences de sécurité\n- Alignement avec les contraintes techniques\n- Respect des standards UX e-commerce\n\n---\n\n💡 **Conseil**: Commencez par lister les parcours utilisateur principaux, puis détaillez chaque user story avec ses critères d'acceptation."
      }
    ],
    "isError": false,
    "metadata": {
      "spec_id": "spec_12345abcdef",
      "current_phase": "requirements",
      "next_tool": "sdd_validate_requirements",
      "template_used": "requirements-template.md"
    }
  }
}
```

#### Étape 3 : Création du Document d'Exigences

**Le développeur utilise le prompt généré et crée un document d'exigences. Voici un extrait :**

```markdown
# Spécification d'Exigences - Authentification Utilisateur

## 1. Introduction et Vision

Cette spécification définit le système d'authentification pour la plateforme e-commerce, 
élément critique pour la sécurité et l'expérience utilisateur.

### Alignement Vision Produit
- Sécurité renforcée pour transactions e-commerce
- UX fluide pour maximiser conversion
- Scalabilité pour croissance utilisateurs

## 2. Exigences Fonctionnelles

### 2.1 Inscription Utilisateur

**US-001**: En tant que visiteur du site, je veux créer un compte rapidement, 
afin de pouvoir effectuer des achats et sauvegarder mes préférences.

**Critères d'acceptation**:
- QUAND je clique sur "S'inscrire" ALORS je vois un formulaire avec email, mot de passe, nom
- QUAND je saisis un email valide ALORS le système vérifie qu'il n'existe pas déjà
- QUAND je confirme l'inscription ALORS je reçois un email de validation
- QUAND je clique sur le lien de validation ALORS mon compte est activé

### 2.2 Connexion Utilisateur

**US-002**: En tant qu'utilisateur enregistré, je veux me connecter en moins de 3 clics, 
afin d'accéder rapidement à mon espace personnel.

**Critères d'acceptation**:
- QUAND je saisis email et mot de passe corrects ALORS je suis connecté en < 2 secondes
- QUAND je coche "Se souvenir de moi" ALORS ma session dure 30 jours
- QUAND je me trompe 3 fois ALORS mon compte est temporairement bloqué (15 min)
```

#### Étape 4 : Validation des Exigences

**Appel MCP** :
```json
{
  "jsonrpc": "2.0",
  "id": "3",
  "method": "tools/call",
  "params": {
    "name": "sdd_validate_requirements",
    "arguments": {
      "spec_id": "spec_12345abcdef",
      "content": "[Le contenu markdown complet du document d'exigences]",
      "validation_level": "standard"
    }
  }
}
```

**Réponse de Validation** :
```json
{
  "jsonrpc": "2.0",
  "id": "3",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "🔍 RAPPORT DE VALIDATION - Exigences user-authentication\n\n✅ **VALIDATION RÉUSSIE** (Score: 92/100)\n\n## 📊 Résumé Qualité\n\n- **User Stories**: 15 total, 14 valides (93%)\n- **Critères d'Acceptation**: 42 total, 40 valides (95%)\n- **Couverture Fonctionnelle**: 95%\n- **Couverture Non Fonctionnelle**: 88%\n- **Standards Sécurité**: 100% ✅\n\n## ✅ Points Forts\n\n### Excellente Structure\n- Format user stories respecté (14/15)\n- Critères EARS bien formés (40/42)\n- Vision produit claire et alignée\n- Couverture complète des parcours utilisateur\n\n### Sécurité Robuste\n- Chiffrement bcrypt spécifié ✅\n- Protection brute force définie ✅\n- Gestion sessions sécurisées ✅\n- Conformité OWASP mentionnée ✅\n\n### UX Bien Pensée\n- Temps de réponse spécifiés (< 2s)\n- Parcours mobile optimisé\n- Accessibilité prise en compte\n- Messages d'erreur utilisateur\n\n## ⚠️ Améliorations Mineures (2)\n\n### 1. User Story US-012 Imprécise\n**Ligne 245**: \"En tant qu'admin, je veux gérer les utilisateurs\"\n**Amélioration**: Spécifier les actions (bloquer, supprimer, modifier rôles)\n**Impact**: Faible - clarification recommandée\n\n### 2. Critère d'Acceptation Manquant\n**Section**: OAuth2 Google (US-008)\n**Manque**: Gestion des erreurs de connexion OAuth\n**Action**: Ajouter critères pour échecs d'autorisation\n\n## 🚀 Recommandations\n\n1. **Prêt pour la phase Design** - Qualité suffisante\n2. **Corrections optionnelles** - Améliorations mineures peuvent être traitées en parallèle\n3. **Focus sécurité** - Excellente prise en compte des enjeux e-commerce\n\n## 📈 Métriques Détaillées\n\n- **Lisibilité**: A (très claire)\n- **Complétude**: A (exhaustive)\n- **Testabilité**: A (critères vérifiables)\n- **Maintenabilité**: B+ (bien structurée)\n\n🎉 **Félicitations !** Cette spécification d'exigences est de haute qualité et prête pour la phase de conception."
      }
    ],
    "isError": false,
    "metadata": {
      "validation_status": "passed",
      "score": 92,
      "errors": 0,
      "warnings": 2,
      "can_proceed_to_design": true,
      "next_action": "sdd_phase_advance"
    }
  }
}
```

#### Étape 5 : Transition vers la Phase Design

**Appel MCP** :
```json
{
  "jsonrpc": "2.0",
  "id": "4",
  "method": "tools/call",
  "params": {
    "name": "sdd_phase_advance",
    "arguments": {
      "spec_id": "spec_12345abcdef",
      "validation_required": true
    }
  }
}
```

**Réponse avec Nouveau Prompt** :
```json
{
  "jsonrpc": "2.0",
  "id": "4",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "🎯 TRANSITION RÉUSSIE vers la phase DESIGN\n\n📋 **Mise à jour spécification**:\n- Spec ID: spec_12345abcdef\n- Phase précédente: Requirements ✅ (validé)\n- Phase actuelle: Design (2/4)\n- Progression: 50% du workflow SDD\n\n📊 **Résumé des exigences validées**:\n- 15 user stories définies\n- 42 critères d'acceptation\n- 8 parcours utilisateur couverts\n- Standards de sécurité e-commerce respectés\n\n🎨 **Prompt généré pour la conception technique**:"
      },
      {
        "type": "text",
        "text": "---\n\n# CONCEPTION TECHNIQUE - user-authentication\n\n## CONTEXTE PROJET\n\n**Projet**: e-commerce-platform  \n**Technologies**: TypeScript, React, Node.js, PostgreSQL  \n**Architecture**: Microservices avec API Gateway  \n\n## EXIGENCES VALIDÉES - RÉSUMÉ\n\n### Fonctionnalités Core\n- ✅ Inscription avec validation email\n- ✅ Connexion/déconnexion sécurisée\n- ✅ Réinitialisation mot de passe\n- ✅ Gestion profil utilisateur\n- ✅ OAuth2 (Google, Facebook)\n- ✅ Administration des comptes\n- ✅ Sessions persistantes (\"Se souvenir\")\n- ✅ Protection brute force\n\n### Contraintes Non Fonctionnelles\n- **Performance**: Connexion < 2 secondes\n- **Sécurité**: bcrypt, sessions sécurisées, OWASP\n- **Scalabilité**: Support 10K utilisateurs simultanés\n- **Conformité**: RGPD, accessibilité WCAG 2.1\n\n## INSTRUCTIONS DE CONCEPTION\n\nCréez une conception technique détaillée suivant design-template.md.\n\n### ARCHITECTURE REQUISE\n\n1. **Vue d'ensemble du système**\n   - Diagramme d'architecture avec composants\n   - Flux de données et interactions\n   - Intégration avec l'existant\n\n2. **Composants applicatifs**\n   ```\n   AuthService (backend)\n   ├── UserController (API REST)\n   ├── AuthMiddleware (JWT validation)\n   ├── PasswordService (bcrypt)\n   ├── SessionManager (Redis)\n   ├── OAuthProvider (Google/Facebook)\n   └── EmailService (validation)\n   \n   AuthModule (frontend)\n   ├── LoginForm (React)\n   ├── RegisterForm (React)\n   ├── ProfileManager (React)\n   ├── AuthContext (state global)\n   └── AuthGuard (protection routes)\n   ```\n\n3. **Modèles de données**\n   - Schéma base de données (PostgreSQL)\n   - Modèles TypeScript/interfaces\n   - Relations et contraintes\n\n4. **APIs et interfaces**\n   - Spécification OpenAPI/Swagger\n   - Schémas de validation\n   - Codes d'erreur standardisés\n\n5. **Sécurité applicative**\n   - Chiffrement et hachage\n   - Gestion des tokens JWT\n   - Protection CSRF/XSS\n   - Rate limiting\n\n### CONTRAINTES TECHNIQUES\n\n**Stack imposée**:\n- Backend: Node.js + Express + TypeScript\n- Frontend: React 18 + TypeScript + Zustand\n- Base: PostgreSQL + Redis (sessions)\n- Auth: JWT + OAuth2 (Passport.js)\n\n**Standards architecture**:\n- Microservice auth autonome\n- API REST avec versioning\n- Validation stricte des entrées\n- Logging structuré (Winston)\n- Tests unitaires (Jest) + intégration\n\n**Performance cibles**:\n- Connexion: < 2 secondes\n- Inscription: < 3 secondes\n- Validation JWT: < 100ms\n- Charge: 10K users simultanés\n\n### INTÉGRATION EXISTANTE\n\n**Services à connecter**:\n- User Profile Service (gestion données)\n- Order Service (historique commandes)\n- Notification Service (emails)\n- Analytics Service (tracking événements)\n\n**Points d'intégration**:\n- API Gateway (routage et auth)\n- Message Bus (événements utilisateur)\n- Shared Database (données communes)\n\n### LIVRABLES ATTENDUS\n\n1. **Diagrammes d'architecture** (Mermaid)\n   - Vue d'ensemble système\n   - Séquences d'authentification\n   - Modèle de données\n\n2. **Spécifications techniques**\n   - APIs REST détaillées\n   - Schémas de validation\n   - Configuration sécurité\n\n3. **Plan d'implémentation**\n   - Décomposition en tâches\n   - Dépendances et prérequis\n   - Estimation des efforts\n\n### VALIDATION AUTOMATIQUE\n\nLa conception sera validée sur:\n✅ Couverture complète des exigences  \n✅ Conformité aux standards techniques  \n✅ Cohérence architecturale  \n✅ Scalabilité et performance  \n✅ Sécurité renforcée e-commerce  \n\n---\n\n💡 **Conseil**: Démarrez par l'architecture globale, puis détaillez chaque composant avec ses responsabilités et interfaces."
      }
    ],
    "isError": false,
    "metadata": {
      "spec_id": "spec_12345abcdef",
      "current_phase": "design",
      "next_tool": "sdd_validate_design",
      "estimated_duration": "3-5 hours",
      "template_used": "design-template.md"
    }
  }
}
```

#### Étape 6 : Création et Validation de la Conception

*[Le développeur crée le document de conception technique en suivant le prompt]*

**Validation de la conception** :
```json
{
  "jsonrpc": "2.0",
  "id": "5",
  "method": "tools/call",
  "params": {
    "name": "sdd_validate_design",
    "arguments": {
      "spec_id": "spec_12345abcdef",
      "content": "[Document de conception technique complet]",
      "validation_level": "standard"
    }
  }
}
```

#### Étape 7 : Transition vers les Tâches et Début d'Exécution

**Après validation de la conception, progression vers les tâches** :
```json
{
  "jsonrpc": "2.0",
  "id": "6",
  "method": "tools/call",
  "params": {
    "name": "sdd_phase_advance",
    "arguments": {
      "spec_id": "spec_12345abcdef"
    }
  }
}
```

*[Génération automatique de la décomposition en tâches atomiques]*

#### Étape 8 : Exécution d'une Tâche Concrète

**Exécution de la première tâche : Création du modèle User** :
```json
{
  "jsonrpc": "2.0",
  "id": "7",
  "method": "tools/call",
  "params": {
    "name": "sdd_execute_task",
    "arguments": {
      "spec_id": "spec_12345abcdef",
      "task_id": "1.1",
      "execution_mode": "interactive",
      "context": {
        "working_directory": "./src",
        "environment": "development"
      }
    }
  }
}
```

**Résultat de l'exécution** :
```json
{
  "jsonrpc": "2.0",
  "id": "7",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "🔄 EXÉCUTION TÂCHE 1.1 - Créer le modèle User\n\n✅ **TÂCHE TERMINÉE AVEC SUCCÈS** (Durée: 28 secondes)\n\n📁 **Fichiers créés/modifiés**:\n- ✅ `src/models/User.ts` (nouveau - 156 lignes)\n- ✅ `src/models/index.ts` (modifié - export ajouté)\n- ✅ `tests/models/User.test.ts` (nouveau - 89 lignes)\n- ✅ `src/types/auth.ts` (nouveau - enums et interfaces)\n\n🧪 **Tests automatiques**:\n- ✅ 12/12 tests unitaires passés\n- ✅ Couverture: 97%\n- ✅ Performance: Tous benchmarks OK\n- ✅ Sécurité: Validation mot de passe OK\n\n📊 **Métriques qualité**:\n- Complexité cyclomatique: 2.3 (Excellent)\n- Maintenabilité: A+\n- Duplication: 0%\n- Standards TypeScript: 100% conforme\n\n🎯 **Fonctionnalités implémentées**:\n- Interface UserAttributes complète\n- Classe User avec validation email/password\n- Méthodes de hachage sécurisé (bcrypt)\n- Gestion des rôles utilisateur\n- Timestamps automatiques\n- Intégration avec BaseModel\n\n📝 **Code généré aligné sur conception**:\n- Respect de l'architecture définie\n- Standards de sécurité e-commerce\n- Interfaces TypeScript strictes\n- Tests exhaustifs incluant edge cases\n\n🔄 **Prochaines tâches suggérées**:\n- 1.2: Créer UserController avec endpoints REST\n- 1.3: Implémenter AuthMiddleware JWT\n- 1.4: Configurer sessions Redis\n\n💡 **Impact sur le projet**:\n- Fondation solide pour l'authentification\n- Sécurité renforcée dès la base\n- Tests robustes pour éviter les régressions\n- Architecture évolutive respectée"
      }
    ],
    "isError": false,
    "metadata": {
      "execution_id": "exec_abc123def456",
      "task_status": "completed",
      "files_created": 3,
      "files_modified": 1,
      "tests_passed": 12,
      "coverage_percentage": 97,
      "quality_score": "A+",
      "next_task_id": "1.2",
      "commit_hash": "a1b2c3d4e5f6"
    }
  }
}
```

#### Étape 9 : Suivi Global du Projet

**Vérification de l'état global** :
```json
{
  "jsonrpc": "2.0",
  "id": "8",
  "method": "tools/call",
  "params": {
    "name": "sdd_project_status",
    "arguments": {
      "project_id": "proj_67890abcdef",
      "include_metrics": true,
      "format": "detailed"
    }
  }
}
```

**Réponse de statut** :
```json
{
  "jsonrpc": "2.0",
  "id": "8",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "📊 STATUT PROJET - e-commerce-platform\n\n## 🎯 Vue d'ensemble\n\n**Projet**: e-commerce-platform (proj_67890abcdef)  \n**Créé**: 15 janvier 2024, 10:30  \n**Dernière activité**: Il y a 2 minutes  \n**Équipe**: 3 développeurs  \n**Stack**: TypeScript, React, Node.js  \n\n## 📋 Spécifications Actives\n\n### 1. user-authentication (spec_12345abcdef)\n- **Phase actuelle**: Tasks (3/4) - 75% complété\n- **Statut**: En cours d'exécution ✅\n- **Priorité**: High\n- **Progression tâches**: 1/15 terminées (7%)\n\n**Phases terminées**:\n- ✅ Requirements (Score: 92/100) - 15 jan 11:15\n- ✅ Design (Score: 88/100) - 15 jan 14:30\n- 🔄 Tasks (En cours) - Démarré 15 jan 15:45\n\n**Dernières activités**:\n- ✅ Tâche 1.1 terminée (Modèle User) - 16:15\n- 🔄 Tâche 1.2 en préparation (UserController)\n\n## 📈 Métriques Globales\n\n### Qualité du Code\n- **Couverture tests**: 97% (Excellent)\n- **Complexité moyenne**: 2.3 (Très bonne)\n- **Dette technique**: 0h (Aucune)\n- **Standards**: 100% conformité\n\n### Performance\n- **Temps moyen validation**: 45s\n- **Temps moyen exécution tâche**: 32s\n- **Taux succès validation**: 100%\n- **Taux succès exécution**: 100%\n\n### Progression\n- **Fonctionnalités spécifiées**: 1\n- **Spécifications validées**: 0.75 (75%)\n- **Tâches terminées**: 1\n- **Fichiers générés**: 4\n- **Tests créés**: 12\n\n## 🔄 Prochaines Étapes\n\n### Immédiat (Aujourd'hui)\n1. **Continuer user-authentication**\n   - Tâche 1.2: UserController (2-3h)\n   - Tâche 1.3: AuthMiddleware (1-2h)\n\n### Cette semaine\n2. **Nouvelle spécification**: product-catalog\n3. **Tests d'intégration**: user-authentication\n4. **Documentation**: APIs générées\n\n### Alertes et Recommandations\n- 🟢 **Tout va bien** - Aucune alerte active\n- 💡 **Conseil**: Maintenir ce rythme de qualité\n- 🎯 **Focus**: Terminer l'authentification avant nouvelle spec\n\n## 📚 Ressources\n\n- [Documentation projet](.sdd/project/)\n- [Templates SDD](.sdd/templates/)\n- [Logs détaillés](.sdd/logs/2024-01-15.log)\n- [Métriques temps réel](http://localhost:3003/dashboard)\n\n---\n\n✨ **Excellent travail !** Le projet démarre sur de solides fondations."
      }
    ],
    "isError": false,
    "metadata": {
      "project_health": "excellent",
      "completion_percentage": 7,
      "quality_score": "A+",
      "next_milestone": "user-authentication completion",
      "estimated_completion": "2024-01-18"
    }
  }
}
```

### Bénéfices Observés de ce Workflow

#### 1. **Traçabilité Complète**
- Chaque étape est documentée et validée
- Historique complet des décisions et modifications
- Liens clairs entre exigences, conception et implémentation

#### 2. **Qualité Garantie**
- Validation automatique à chaque phase
- Standards de qualité imposés et vérifiés
- Tests automatiques générés et exécutés

#### 3. **Efficacité Développeur**
- Prompts contextuels réduisant l'effort de réflexion
- Génération automatique de code respectant l'architecture
- Feedback immédiat sur la qualité et conformité

#### 4. **Cohérence Architecturale**
- Respect des contraintes techniques définies
- Alignement avec la vision produit
- Intégration harmonieuse avec l'existant

#### 5. **Visibilité Projet**
- Statut en temps réel de chaque spécification
- Métriques de progression et qualité
- Alertes préventives sur les dérives

Ce workflow complet démontre comment le serveur MCP SDD transforme un processus de développement traditionnel en un flux automatisé, guidé et de haute qualité, tout en préservant la créativité et l'expertise des développeurs.

### Configuration et Déploiement

Ce document présente une architecture complète pour un serveur MCP supportant le workflow SDD. L'approche orchestrée recommandée offre un équilibre optimal entre simplicité d'implémentation et fonctionnalités avancées, tout en préservant la possibilité d'évolution vers une architecture distribuée selon les besoins futurs.

Les deux approches respectent les principes du Model Context Protocol et permettent une intégration fluide avec les LLM clients, tout en conservant la robustesse et la traçabilité du workflow SDD existant.
