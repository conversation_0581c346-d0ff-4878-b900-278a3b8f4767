# Commande de Correction de Bug

Implémentez la correction pour le bug analysé.

## Utilisation
```
/bug-fix [nom-du-bug]
```

## Vue d'ensemble de la phase
**Votre rôle** : Implémenter la solution basée sur l'analyse approuvée

Ceci est la Phase 3 du workflow de correction de bug. Votre objectif est d’implémenter la correction en suivant les conventions du projet.

## Instructions

Vous travaillez sur la phase d’implémentation de la correction du bug.

1. **Prérequis & Chargement du contexte**
   - Assurez-vous que analysis.md existe et est approuvé

   **Charger TOUT le contexte une fois (Chargement hiérarchique du contexte) :**
   ```bash
   # Charger les documents de pilotage (si disponibles)
   claude-code-spec-workflow get-steering-context
   ```

   **Documents de bug à lire directement :**
   - `.sdd/bugs/{nom-du-bug}/report.md`
   - `.sdd/bugs/{nom-du-bug}/analysis.md`
   - Comprenez complètement l’approche de correction prévue

2. **Processus d’implémentation**
   1. **Suivre le plan d’implémentation**
      - Exécutez les changements exactement comme indiqué dans analysis.md
      - Faites des modifications ciblées et minimales
      - Suivez les modèles et conventions de code existants

   2. **Modifications du code**
      - Implémentez la correction selon les standards du projet
      - Ajoutez une gestion d’erreur appropriée
      - Incluez des aides au débogage ou des logs si nécessaire
      - Mettez à jour ou ajoutez des tests comme spécifié

   3. **Vérifications de qualité**
      - Vérifiez que la correction traite la cause racine
      - Assurez-vous qu’il n’y a pas d’effets secondaires non intentionnés
      - Respectez le style et les conventions de code
      - Exécutez les tests et vérifications

3. **Directives d’implémentation**
   - **Suivre les documents de pilotage** : Respectez les modèles dans tech.md et les conventions dans structure.md
   - **Faire des changements minimaux** : Corrigez uniquement ce qui est nécessaire
   - **Préserver le comportement existant** : Ne cassez pas les fonctionnalités non liées
   - **Utiliser les modèles existants** : Exploitez les modèles et utilitaires de code établis
   - **Ajouter des tests appropriés** : Assurez-vous que le bug ne reviendra pas

4. **Exigences de test**
   - Testez le scénario spécifique du bug
   - Vérifiez que la fonctionnalité liée fonctionne toujours
   - Exécutez la suite de tests existante si disponible
   - Ajoutez des tests de non-régression pour ce bug

5. **Mises à jour de la documentation**
   - Mettez à jour les commentaires du code si nécessaire
   - Documentez tout changement non évident
   - Mettez à jour les messages d’erreur si applicable

## Règles d’implémentation

### Qualité du code
- Respectez les standards de codage du projet
- Utilisez les utilitaires et modèles existants
- Ajoutez une gestion d’erreur appropriée
- Incluez des commentaires significatifs pour la logique complexe

### Stratégie de test
- Testez les étapes de reproduction originales du bug
- Vérifiez que la correction ne casse pas la fonctionnalité liée
- Ajoutez des tests pour éviter la régression
- Exécutez la suite de tests complète si disponible

### Gestion des changements
- Faites des changements atomiques et ciblés
- Documentez l’approche de correction
- Préservez les contrats d’API existants
- Considérez la compatibilité ascendante

## Processus de finalisation

1. **Implémenter la correction**
   - Faites les modifications de code nécessaires
   - Suivez le plan d’implémentation de analysis.md
   - Assurez-vous que le code respecte les conventions du projet

2. **Vérifier l’implémentation**
   - Testez que le bug original est résolu
   - Vérifiez qu’aucun nouveau problème n’est introduit
   - Exécutez les tests et vérifications pertinents

3. **Mettre à jour la documentation**
   - Documentez les changements effectués
   - Mettez à jour les commentaires ou docs pertinents

4. **Confirmer la finalisation**
   - Présentez un résumé des changements effectués
   - Montrez les résultats des tests confirmant la correction

5. **Confirmation finale**
   - Demandez : « La correction a été implémentée et revue. Devons-nous procéder à la vérification ? »
   - **CRITIQUE** : Attendez l’approbation de l’utilisateur avant de poursuivre

## Règles critiques
- **UNIQUEMENT** implémenter la correction décrite dans l’analyse approuvée
- **TOUJOURS** tester la correction minutieusement
- **JAMAIS** faire des changements au-delà du périmètre de la correction prévue
- **DOIT** attendre l’approbation de l’utilisateur avant de passer à la vérification

## Prochaine phase
Après approbation, passez à `/bug-verify`.
