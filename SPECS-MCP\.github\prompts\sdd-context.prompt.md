---
mode: agent
model: <PERSON> 4
tools: ['codebase', 'search', 'findTestFiles']
description: Génère automatiquement les 3 fichiers de contexte projet SDD avec validation utilisateur à chaque étape.
---

# Génération du Contexte Projet SDD avec Validation

Tu vas maintenant exécuter le processus de génération de la documentation contexte projet selon la méthodologie Spec-Driven Development (SDD) avec validation utilisateur à chaque étape.

## Objectif

Créer la mémoire centrale du projet qui servira de référence unique pour tous les développements ultérieurs en générant séquentiellement les 3 fichiers contexte :

- `.sdd/project/product.md` - Documentation produit et vision
- `.sdd/project/structure.md` - Architecture et organisation du projet  
- `.sdd/project/tech.md` - Spécifications techniques et implémentation

## Workflow avec Validation à Chaque Étape

### Règle Fondamentale
**Génération séquentielle Product → Structure → Tech avec validation explicite utilisateur obligatoire à chaque étape.**

### Interaction avec l'Utilisateur
- **TOUJOURS** demander l'approbation explicite avant de passer d'un fichier au suivant
- **NE JAMAIS** supposer que l'utilisateur approuve un document sans confirmation explicite
- **TOUJOURS** continuer le cycle feedback-révision jusqu'à obtention d'une approbation explicite
- L'utilisateur DOIT donner une approbation explicite ("oui", "approuvé", "c'est bon", etc.)

## Phase 1 : Product (product.md)

### Génération Product
1. **Vérifier l'existence** du répertoire `.sdd/project/`
2. **Analyser le contexte** projet disponible en profondeur
3. **Créer le fichier** `.sdd/project/product.md`
4. **Générer automatiquement** toutes les sections :
   - Vision et mission du produit
   - Objectifs stratégiques avec métriques SMART
   - Utilisateurs cibles et personas
   - Fonctionnalités principales et roadmap
   - Critères de succès mesurables

### Validation Product
- Demander explicitement : "Le fichier product.md vous convient-il ? Si oui, nous pouvons passer à structure.md."
- Si modifications demandées : réviser et redemander approbation
- Continuer le cycle jusqu'à approbation explicite
- **NE PAS** passer au structure sans approbation explicite

## Phase 2 : Structure (structure.md)

### Prérequis
- S'assurer que product.md existe et est validé par l'utilisateur

### Génération Structure
- **Créer le fichier** `.sdd/project/structure.md`
- **Analyser l'organisation** optimale selon le type de projet
- **Générer automatiquement** toutes les sections :
  - Architecture des dossiers et organisation
  - Conventions de nommage et standards
  - Patterns et bonnes pratiques
  - Intégration méthodologie SDD
  - Structure évolutive et maintenable

### Validation Structure
- Demander explicitement : "Le fichier structure.md vous convient-il ? Si oui, nous pouvons passer à tech.md."
- Apporter des modifications si demandées
- Continuer le cycle feedback-révision jusqu'à approbation explicite
- **NE PAS** passer au tech sans approbation explicite

## Phase 3 : Tech (tech.md)

### Prérequis
- S'assurer que structure.md existe et est validé par l'utilisateur

### Génération Tech
- **Créer le fichier** `.sdd/project/tech.md`
- **Analyser les spécifications** techniques nécessaires
- **Générer automatiquement** toutes les sections :
  - Stack technique et technologies
  - Configuration et environnements
  - APIs et interfaces
  - Contraintes et exigences non-fonctionnelles
  - Standards de développement

### Validation Tech
- Demander explicitement : "Le fichier tech.md vous convient-il ?"
- Apporter des modifications si demandées
- Continuer le cycle feedback-révision jusqu'à approbation explicite
- **S'ARRÊTER** une fois le document tech approuvé

## Principes de Génération

### Méthodologie Mémoire
- **Centralisation** : Toute l'information critique centralisée dans `.sdd/project/`
- **Référencement** : Les templates servent de modèles canoniques
- **Cohérence** : Une seule source de vérité maintenue
- **Traçabilité** : Liens explicites entre tous les documents
- **Évolutivité** : Structure permettant les mises à jour continues

### Standards de Qualité
- Conformité aux templates de référence
- Cohérence terminologique inter-documents
- Complétude de toutes les sections obligatoires
- Application rigoureuse des instructions spécifiques
- Maintien de la traçabilité et des références croisées

## Contraintes Techniques

### Structure et Nommage
- **Destination obligatoire** : `.sdd/project/`
- **Noms de fichiers** : `product.md`, `structure.md`, `tech.md`
- **Encodage** : UTF-8 avec métadonnées YAML si nécessaire
- **Format** : Markdown strict selon les bonnes pratiques

### Approche de Génération
- **Analyse approfondie** : Examiner tous les fichiers existants du projet
- **Génération intelligente** : Créer du contenu substantiel et pertinent pour chaque section
- **Éviter les placeholders** : Personnaliser selon le contexte projet spécifique
- **Maintenir la cohérence** : Entre les 3 documents

## Cycle de Révision

### Workflow de Validation
1. Présenter le document initial
2. Demander validation explicite avec question spécifique
3. Si modifications demandées : réviser et re-valider
4. Répéter jusqu'à approbation explicite
5. Informer de la progression vers l'étape suivante

### Finalisation
Après approbation du document tech :
```
**Workflow de contexte projet terminé !**

Les 3 fichiers de contexte projet SDD sont maintenant créés et servent de référence unique pour tous les développements futurs.
```

## Instructions d'Exécution

**COMMENCER MAINTENANT** avec la génération du contexte projet :

1. **Vérifier** l'existence du répertoire `.sdd/project/`
2. **Analyser** l'ensemble du contexte projet disponible
3. **Générer** le fichier product.md avec validation
4. **Générer** le fichier structure.md avec validation
5. **Générer** le fichier tech.md avec validation
6. **Finaliser** avec confirmation de completion

**Objectif** : Créer 3 fichiers contexte complets et validés, servant de base de référence solide pour toutes les spécifications futures selon la méthodologie SDD.