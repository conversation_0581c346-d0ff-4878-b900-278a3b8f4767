# Commande d'Analyse de Bug

Enquêter et analyser la cause racine d'un bug signalé.

## Utilisation
```
/bug-analyze [nom-du-bug]
```

## Vue d'ensemble de la phase
**Votre rôle** : Enquêter sur le bug et identifier la cause racine

Ceci est la Phase 2 du workflow de correction de bug. Votre objectif est de comprendre pourquoi le bug se produit et de planifier l'approche de correction.

## Instructions

**Processus d'analyse manuelle** :

1. **Prérequis**
   - Vérifier que report.md existe et est complet
   - Charger le rapport de bug pour le contexte
   - **Charger les documents de pilotage** : 
     ```bash
     # Windows :
     claude-code-spec-workflow get-content "C:\path\to\project\.sdd\project\tech.md"
     claude-code-spec-workflow get-content "C:\path\to\project\.sdd\project\structure.md"
     
     # macOS/Linux :
     claude-code-spec-workflow get-content "/path/to/project/.sdd/project/tech.md"
     claude-code-spec-workflow get-content "/path/to/project/.sdd/project/structure.md"
     ```
   - Comprendre complètement le problème signalé

2. **Processus d'investigation**
   1. **Investigation du code**
      - Rechercher dans la base de code la fonctionnalité concernée
      - Identifier les fichiers, fonctions et composants impliqués
      - Cartographier le flux de données et identifier les points de défaillance potentiels
      - Rechercher des problèmes ou des schémas similaires

   2. **Analyse de la cause racine**
      - Déterminer la cause sous-jacente du bug
      - Identifier les facteurs contributifs
      - Comprendre pourquoi les tests existants n'ont pas détecté cela
      - Évaluer l'impact et les risques

   3. **Planification de la solution**
      - Concevoir la stratégie de correction
      - Considérer des approches alternatives
      - Planifier la stratégie de test
      - Identifier les risques potentiels

3. **Créer le document d'analyse**
   - **Modèle à suivre** : Utiliser le modèle d'analyse de bug du contexte préchargé ci-dessus (ne pas recharger)
   - **Lire et suivre** : Utiliser le modèle d'analyse de bug et suivre toutes les sections précisément
   - Documenter les résultats de l'investigation selon la structure du modèle

## Utilisation du modèle
- **Suivre la structure exacte** : Utiliser `.claude/templates/bug-analysis-template.md` précisément
- **Inclure toutes les sections** : Ne pas omettre de sections requises du modèle
- **Analyse détaillée** : Suivre le format du modèle pour une investigation complète

4. **Directives d'investigation**
   - **Respecter les standards de tech.md** : Comprendre les schémas existants avant de proposer des changements
   - **Respecter structure.md** : Savoir où placer les corrections
   - **Rechercher minutieusement** : Chercher des utilitaires existants, bugs similaires, code lié
   - **Penser systématiquement** : Considérer le flux de données, la gestion des erreurs, les cas limites
   - **Planifier les tests** : Comment vérifier que la correction fonctionne

5. **Processus d'approbation**
   - Présenter le document d'analyse complet
   - **Montrer les opportunités de réutilisation de code** : Noter les utilitaires existants pouvant aider
   - **Mettre en avant les points d'intégration** : Montrer comment la correction s'intègre à l'architecture existante
   - Demander : "Cette analyse semble-t-elle correcte ? Si oui, nous pouvons procéder à la correction."
   - Intégrer les retours et révisions
   - Continuer jusqu'à approbation explicite
   - **CRITIQUE** : Ne pas poursuivre sans approbation explicite

## Directives d'analyse

### Investigation du code
- Utiliser des outils de recherche pour trouver le code pertinent
- Comprendre les schémas de gestion d'erreur existants
- Rechercher des fonctionnalités similaires qui fonctionnent correctement
- Vérifier les changements récents qui pourraient avoir causé le problème

### Identification de la cause racine
- Ne pas se contenter de corriger les symptômes - trouver la vraie cause
- Considérer les cas limites et les conditions d'erreur
- Rechercher des problèmes de conception vs des bugs d'implémentation
- Comprendre le comportement attendu vs le comportement réel

### Conception de la solution
- Privilégier des corrections minimales et ciblées
- Réutiliser les schémas et utilitaires existants
- Considérer la rétrocompatibilité
- Planifier la prévention future de bugs similaires

## Règles critiques
- **NE JAMAIS** passer à la phase suivante sans approbation explicite de l'utilisateur
- N'accepter que des réponses affirmatives claires : "oui", "approuvé", "c'est bon", etc.
- Si l'utilisateur donne un retour, faire les révisions et demander à nouveau l'approbation
- Continuer le cycle de révision jusqu'à obtention de l'approbation explicite

## Phase suivante
Après approbation, passer à `/bug-fix`.
