# PHASE 2 - CONSOLIDATION DÉTAILLÉE (3 SPRINTS)
*Plan d'exécution opérationnel - <PERSON><PERSON><PERSON> 8-12*

## VUE D'ENSEMBLE PHASE 2

### OBJECTIF GLOBAL
Migration transparente vers PostgreSQL et validation métier réelle, transformant le MVP fonctionnel en solution consolidée et robuste sans breaking changes.

### ARCHITECTURE CIBLE PHASE 2

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP Client    │────│  JSON-RPC 2.0   │────│ Workflow Manager│
│     (LLM)       │    │     Server      │    │   (Inchangé)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                │                       │
                        ┌─────────────────┐    ┌─────────────────┐
                        │ Prompt          │    │ State Manager   │
                        │ Orchestrator    │    │  (PostgreSQL)   │
                        └─────────────────┘    └─────────────────┘
                                │                       │
                                │                       │
                        ┌─────────────────┐    ┌─────────────────┐
                        │ Validation      │    │ Migration       │
                        │ Engine (Real)   │    │ Scripts         │
                        └─────────────────┘    └─────────────────┘
```

### ROADMAP 3 SPRINTS

```
Sprint 1         Sprint 2         Sprint 3
(Sem 8-9)       (Sem 10-11)      (Sem 12)
INFRASTRUCTURE  VALIDATION       ORCHESTRATOR
│               │                │
├─ PostgreSQL   ├─ Requirements   ├─ Prompt Engine
├─ Migration    ├─ Design Valid   ├─ Handlebars
├─ Schema       ├─ Tasks Valid    ├─ Context Mgmt
└─ Tests        ├─ Rule Engine    └─ Tests e2e
                └─ Tests métier
```

### ÉQUIPE ET RESPONSABILITÉS

**Tech Lead** : Architecture migration, validation interfaces stables
**Développeur Senior** : PostgreSQL + ORM, validation métier
**Développeur Backend** : Prompt Orchestrator, templates Handlebars
**DevOps Engineer** : Scripts migration, monitoring performance
**Product Owner** : Validation métier, acceptance criteria

---

## SPRINT 1 - INFRASTRUCTURE CONSOLIDÉE (Semaines 8-9)

### SPRINT GOAL
"Migration PostgreSQL transparente avec performance optimisée"

### PRÉREQUIS
Phase 1 MVP déployée et opérationnelle avec données de test

### USER STORIES DÉTAILLÉES

#### US-015 : Migration PostgreSQL (21 points)
**EN TANT QUE** système MCP SDD  
**JE VEUX** migrer de la persistence fichiers vers PostgreSQL  
**AFIN D'** améliorer performance, fiabilité et scalabilité

**Acceptance Criteria :**
- Schéma PostgreSQL optimisé avec indexes et contraintes
- Script migration automatique avec validation intégrité
- Interface `IStateRepository` inchangée (zero breaking changes)
- Backup/restore automatique pour rollback sécurisé
- Performance ≥ 2x amélioration vs fichiers JSON

**Schéma PostgreSQL Optimisé :**
```sql
-- Core tables
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  current_phase spec_phase_enum NOT NULL DEFAULT 'new',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  
  CONSTRAINT projects_name_check CHECK (length(name) >= 3)
);

CREATE TABLE specifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  phase spec_phase_enum NOT NULL,
  content TEXT NOT NULL,
  version INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by VARCHAR(100),
  
  UNIQUE(project_id, phase, version)
);

CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  status task_status_enum NOT NULL DEFAULT 'pending',
  priority INTEGER DEFAULT 0,
  estimated_hours NUMERIC(5,2),
  dependencies UUID[],
  assigned_to VARCHAR(100),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT tasks_title_check CHECK (length(title) >= 5)
);

CREATE TABLE project_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  action VARCHAR(50) NOT NULL,
  details JSONB,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id VARCHAR(100)
);

-- Enums
CREATE TYPE spec_phase_enum AS ENUM (
  'new', 'requirements', 'design', 'tasks', 'execution', 'completed'
);

CREATE TYPE task_status_enum AS ENUM (
  'pending', 'in_progress', 'completed', 'failed', 'skipped'
);

-- Indexes pour performance
CREATE INDEX idx_projects_phase ON projects(current_phase);
CREATE INDEX idx_projects_updated ON projects(updated_at DESC);
CREATE INDEX idx_specs_project_phase ON specifications(project_id, phase);
CREATE INDEX idx_tasks_project_status ON tasks(project_id, status);
CREATE INDEX idx_history_project_time ON project_history(project_id, timestamp DESC);
CREATE INDEX idx_specs_content_fts ON specifications USING gin(to_tsvector('english', content));

-- Functions pour audit automatique
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_projects_updated_at 
  BEFORE UPDATE ON projects 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### US-016 : Script Migration Automatique (13 points)
**EN TANT QUE** équipe DevOps  
**JE VEUX** un script migration fiable et rollback  
**AFIN DE** migrer sans risque de perte données

**Acceptance Criteria :**
- Validation complète des données source avant migration
- Migration incrémentale avec progress tracking
- Vérification intégrité post-migration
- Rollback automatique en cas d'échec
- Logs détaillés pour audit et debugging

**Architecture Script Migration :**
```typescript
// src/migration/FilesToPostgresMigrator.ts
export class FilesToPostgresMigrator {
  constructor(
    private fileRepository: FileStateRepository,
    private postgresRepository: PostgresStateRepository,
    private logger: Logger,
    private config: MigrationConfig
  ) {}
  
  async migrate(): Promise<MigrationResult> {
    const migrationId = generateMigrationId();
    
    try {
      // 1. Validation pré-migration
      await this.validateSourceData();
      
      // 2. Backup complet
      const backupPath = await this.createBackup();
      
      // 3. Migration par batch
      const result = await this.executeMigration(migrationId);
      
      // 4. Validation post-migration
      await this.validateMigration(result);
      
      // 5. Cleanup optionnel
      if (this.config.cleanupAfterSuccess) {
        await this.cleanupSourceFiles(backupPath);
      }
      
      return result;
      
    } catch (error) {
      // Rollback automatique
      await this.rollback(migrationId);
      throw new MigrationError(`Migration failed: ${error.message}`, error);
    }
  }
  
  private async validateSourceData(): Promise<void> {
    const projects = await this.fileRepository.listProjects();
    
    for (const project of projects) {
      // Validation structure projet
      if (!project.id || !project.name) {
        throw new ValidationError(`Invalid project structure: ${project}`);
      }
      
      // Validation spécifications
      const specs = await this.fileRepository.listSpecifications(project.id);
      for (const spec of specs) {
        if (!spec.phase || !spec.content) {
          throw new ValidationError(`Invalid spec for project ${project.id}`);
        }
      }
    }
  }
  
  private async executeMigration(migrationId: string): Promise<MigrationResult> {
    const projects = await this.fileRepository.listProjects();
    const result: MigrationResult = {
      migrationId,
      projectsCount: projects.length,
      migratedProjects: 0,
      migratedSpecs: 0,
      migratedTasks: 0,
      errors: []
    };
    
    for (const project of projects) {
      try {
        await this.migrateProject(project, result);
        result.migratedProjects++;
        
        // Progress tracking
        const progress = (result.migratedProjects / result.projectsCount) * 100;
        this.logger.info('Migration progress', { 
          migrationId, 
          progress: `${progress.toFixed(1)}%`,
          project: project.name 
        });
        
      } catch (error) {
        result.errors.push({ 
          projectId: project.id, 
          error: error.message 
        });
        this.logger.error('Project migration failed', { 
          migrationId, 
          projectId: project.id, 
          error 
        });
      }
    }
    
    return result;
  }
}
```

#### US-017 : PostgresStateRepository (8 points)
**EN TANT QUE** Workflow Manager  
**JE VEUX** une implémentation PostgreSQL de IStateRepository  
**AFIN DE** maintenir la compatibilité avec meilleure performance

**Acceptance Criteria :**
- Interface `IStateRepository` respectée à 100%
- Connection pooling et gestion erreurs robuste
- Transactions pour opérations multi-tables
- Performance queries optimisées avec indexes
- Tests compatibilité avec FileStateRepository

```typescript
// src/repositories/PostgresStateRepository.ts
export class PostgresStateRepository implements IStateRepository {
  constructor(
    private db: Pool,
    private logger: Logger
  ) {}
  
  async createProject(projectData: ProjectCreate): Promise<Project> {
    const client = await this.db.connect();
    
    try {
      await client.query('BEGIN');
      
      // Insert project
      const projectResult = await client.query(`
        INSERT INTO projects (name, description, metadata)
        VALUES ($1, $2, $3)
        RETURNING *
      `, [projectData.name, projectData.description, projectData.metadata]);
      
      const project = projectResult.rows[0];
      
      // Log création
      await client.query(`
        INSERT INTO project_history (project_id, action, details)
        VALUES ($1, $2, $3)
      `, [project.id, 'project_created', { name: project.name }]);
      
      await client.query('COMMIT');
      
      this.logger.info('Project created', { projectId: project.id });
      return this.mapProjectFromRow(project);
      
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Project creation failed', { error, projectData });
      throw error;
    } finally {
      client.release();
    }
  }
  
  async getProject(id: string): Promise<Project | null> {
    try {
      const result = await this.db.query(`
        SELECT p.*, 
               COUNT(DISTINCT s.id) as specs_count,
               COUNT(DISTINCT t.id) as tasks_count
        FROM projects p
        LEFT JOIN specifications s ON p.id = s.project_id
        LEFT JOIN tasks t ON p.id = t.project_id
        WHERE p.id = $1
        GROUP BY p.id
      `, [id]);
      
      if (result.rows.length === 0) return null;
      
      return this.mapProjectFromRow(result.rows[0]);
      
    } catch (error) {
      this.logger.error('Project retrieval failed', { error, projectId: id });
      throw error;
    }
  }
  
  // Connection pooling configuration
  private static createPool(config: DatabaseConfig): Pool {
    return new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
      password: config.password,
      min: config.minConnections || 5,
      max: config.maxConnections || 20,
      idleTimeoutMillis: config.idleTimeout || 30000,
      connectionTimeoutMillis: config.connectionTimeout || 10000,
    });
  }
}
```

### PLANNING DÉTAILLÉ SPRINT 1

#### SEMAINE 8
**Lundi :** Sprint Planning (2h) + Setup environnement PostgreSQL (DevOps)
- Sprint Planning avec revue dépendances Phase 1
- Provisioning infrastructure PostgreSQL (dev/staging)
- Configuration Prisma/TypeORM pour ORM

**Mardi :** Schéma PostgreSQL + migrations Prisma (Tech Lead + Développeur Senior)
- Design schéma optimisé avec contraintes
- Setup Prisma avec génération types TypeScript
- Création migrations initiales avec rollback

**Mercredi :** PostgresStateRepository implémentation (Développeur Senior)
- Implémentation complète interface IStateRepository
- Connection pooling et gestion erreurs
- Tests unitaires avec base test

**Jeudi :** Script migration développement (Tech Lead + DevOps)
- Logic migration fichiers → PostgreSQL
- Validation données et intégrité
- Progress tracking et logging

**Vendredi :** Tests migration sur données réelles (Équipe complète)
- Tests avec projets Phase 1
- Validation performance et rollback
- Debug et optimisations

#### SEMAINE 9
**Lundi :** Finalisation script migration (Tech Lead + DevOps)
- Robustesse script avec cas edge
- Monitoring et métriques migration
- Documentation migration procedure

**Mardi :** Tests performance PostgreSQL vs Fichiers (Développeur Senior + DevOps)
- Benchmark queries courantes
- Optimisation indexes et requêtes
- Load testing avec données volumineuses

**Mercredi :** Tests intégration complète (Équipe complète)
- Tests non-regression outils MCP
- Validation interface IStateRepository
- Tests concurrence et thread safety

**Jeudi :** Migration environnement staging (DevOps + équipe)
- Migration données réelles staging
- Validation fonctionnalité complète
- Tests end-to-end workflow SDD

**Vendredi :** Sprint Review/Demo migration (Product Owner + équipe)
- Démonstration migration transparente
- Métriques performance PostgreSQL
- Validation acceptance criteria

### TESTS CRITIQUES SPRINT 1

#### Tests Migration Intégrité
```typescript
describe('FilesToPostgresMigrator', () => {
  test('migrates all projects without data loss', async () => {
    // Setup données test complexes
    const testProjects = await createTestProjects(50);
    
    // Migration
    const result = await migrator.migrate();
    
    // Validation intégrité
    expect(result.migratedProjects).toBe(50);
    expect(result.errors).toHaveLength(0);
    
    // Validation données par projet
    for (const project of testProjects) {
      const migratedProject = await postgresRepo.getProject(project.id);
      expect(migratedProject).toEqual(project);
      
      // Validation spécifications
      const specs = await postgresRepo.listSpecifications(project.id);
      expect(specs).toHaveLength(project.specifications.length);
    }
  });
  
  test('rollbacks automatically on failure', async () => {
    // Simulate failure mid-migration
    const mockError = new Error('Database connection lost');
    jest.spyOn(postgresRepo, 'createProject').mockRejectedValueOnce(mockError);
    
    await expect(migrator.migrate()).rejects.toThrow('Migration failed');
    
    // Verify rollback
    const projects = await postgresRepo.listProjects();
    expect(projects).toHaveLength(0);
  });
});
```

#### Tests Performance PostgreSQL
```typescript
describe('PostgreSQL Performance', () => {
  test('project queries faster than file system', async () => {
    // Setup 1000 projets test
    await setupLargeDataset(1000);
    
    // Benchmark file repository
    const fileStartTime = Date.now();
    await fileRepo.listProjects();
    const fileDuration = Date.now() - fileStartTime;
    
    // Benchmark PostgreSQL repository
    const pgStartTime = Date.now();
    await postgresRepo.listProjects();
    const pgDuration = Date.now() - pgStartTime;
    
    // PostgreSQL doit être au moins 2x plus rapide
    expect(pgDuration).toBeLessThan(fileDuration / 2);
  });
});
```

### LIVRABLES SPRINT 1
- [x] PostgreSQL schéma optimisé avec indexes
- [x] Script migration automatique avec validation
- [x] PostgresStateRepository fonctionnelle
- [x] Tests migration sur données réelles
- [x] Performance ≥ 2x amélioration confirmée

---

## SPRINT 2 - VALIDATION MÉTIER RÉELLE (Semaines 10-11)

### SPRINT GOAL
"Validation métier fonctionnelle remplaçant les stubs"

### PRÉREQUIS
Sprint 1 completé avec PostgreSQL opérationnel et migration validée

### USER STORIES DÉTAILLÉES

#### US-018 : Requirements Validator (13 points)
**EN TANT QUE** serveur MCP  
**JE VEUX** valider la qualité des exigences selon standards EARS  
**AFIN D'** assurer spécifications complètes et cohérentes

**Acceptance Criteria :**
- Validation format user stories (Qui/Quoi/Pourquoi)
- Vérification critères acceptation format EARS
- Score qualité avec suggestions amélioration
- Configuration règles métier extensible
- Performance <100ms pour spécification standard

**Critères Validation Requirements :**
```typescript
// src/validation/RequirementsValidator.ts
export class RequirementsValidator extends BaseValidator {
  private readonly rules: ValidationRule[] = [
    new UserStoryFormatRule(),
    new AcceptanceCriteriaRule(),
    new CompletenessRule(),
    new ConsistencyRule(),
    new EARSComplianceRule()
  ];
  
  protected async validateInternal(
    content: string,
    context: ValidationContext
  ): Promise<ValidationResult> {
    const extractedElements = this.extractRequirementsElements(content);
    const validationErrors: ValidationError[] = [];
    let score = 1.0;
    
    // Validation User Stories
    const userStories = extractedElements.userStories;
    if (userStories.length === 0) {
      validationErrors.push({
        type: 'missing_user_stories',
        message: 'Aucune user story détectée',
        severity: 'high',
        line: 0
      });
      score -= 0.3;
    } else {
      for (const story of userStories) {
        const storyErrors = this.validateUserStory(story);
        validationErrors.push(...storyErrors);
        score -= storyErrors.length * 0.1;
      }
    }
    
    // Validation Critères Acceptation EARS
    const acceptanceCriteria = extractedElements.acceptanceCriteria;
    for (const criteria of acceptanceCriteria) {
      const earsErrors = this.validateEARSFormat(criteria);
      validationErrors.push(...earsErrors);
      score -= earsErrors.length * 0.05;
    }
    
    return {
      valid: validationErrors.length === 0,
      score: Math.max(score, 0),
      message: this.generateValidationMessage(validationErrors),
      suggestions: this.generateSuggestions(validationErrors),
      details: {
        userStoriesCount: userStories.length,
        acceptanceCriteriaCount: acceptanceCriteria.length,
        errors: validationErrors
      }
    };
  }
  
  private validateUserStory(story: UserStory): ValidationError[] {
    const errors: ValidationError[] = [];
    
    // Format standard: "En tant que [rôle], je veux [action], afin de [bénéfice]"
    const userStoryPattern = /En tant que (.+), je veux (.+), afin de (.+)/i;
    
    if (!userStoryPattern.test(story.description)) {
      errors.push({
        type: 'invalid_user_story_format',
        message: `User story "${story.title}" ne respecte pas le format standard`,
        severity: 'medium',
        line: story.line,
        suggestion: 'Utilisez le format: "En tant que [rôle], je veux [action], afin de [bénéfice]"'
      });
    }
    
    // Validation longueur et clarté
    if (story.description.length < 20) {
      errors.push({
        type: 'user_story_too_short',
        message: `User story "${story.title}" trop courte pour être claire`,
        severity: 'low',
        line: story.line
      });
    }
    
    return errors;
  }
  
  private validateEARSFormat(criteria: AcceptanceCriteria): ValidationError[] {
    const errors: ValidationError[] = [];
    
    // EARS patterns: QUAND/ALORS, SI/ALORS, TANT QUE, OÙ, APRÈS
    const earsPatterns = [
      /QUAND .+ ALORS .+/i,
      /SI .+ ALORS .+/i,
      /TANT QUE .+/i,
      /OÙ .+/i,
      /APRÈS .+/i
    ];
    
    const hasValidEARSFormat = earsPatterns.some(pattern => 
      pattern.test(criteria.description)
    );
    
    if (!hasValidEARSFormat) {
      errors.push({
        type: 'invalid_ears_format',
        message: 'Critère acceptation ne suit pas le format EARS',
        severity: 'medium',
        line: criteria.line,
        suggestion: 'Utilisez: QUAND [condition] ALORS [résultat]'
      });
    }
    
    return errors;
  }
}
```

#### US-019 : Design Validator (13 points)
**EN TANT QUE** serveur MCP  
**JE VEUX** valider cohérence de la conception  
**AFIN D'** assurer architecture alignée avec exigences

**Acceptance Criteria :**
- Validation sections obligatoires design (architecture, composants, données)
- Cohérence avec tech.md et requirements.md
- Détection patterns architecture anti-patterns
- Suggestions optimisation et bonnes pratiques
- Intégration avec bases connaissances architecture

```typescript
// src/validation/DesignValidator.ts
export class DesignValidator extends BaseValidator {
  constructor(
    private architecturePatterns: ArchitecturePatternDatabase,
    private techStackValidator: TechStackValidator
  ) {
    super();
  }
  
  protected async validateInternal(
    content: string,
    context: ValidationContext
  ): Promise<ValidationResult> {
    // Récupération contexte projet
    const requirements = await this.getProjectRequirements(context.projectId);
    const techSpec = await this.getProjectTechSpec(context.projectId);
    
    const validationErrors: ValidationError[] = [];
    let score = 1.0;
    
    // 1. Validation structure document
    const structureErrors = this.validateDocumentStructure(content);
    validationErrors.push(...structureErrors);
    
    // 2. Validation cohérence avec requirements
    const alignmentErrors = await this.validateRequirementsAlignment(
      content, requirements
    );
    validationErrors.push(...alignmentErrors);
    
    // 3. Validation tech stack cohérence
    if (techSpec) {
      const techErrors = await this.validateTechStackAlignment(
        content, techSpec
      );
      validationErrors.push(...techErrors);
    }
    
    // 4. Détection anti-patterns
    const antiPatternErrors = this.detectAntiPatterns(content);
    validationErrors.push(...antiPatternErrors);
    
    // Calcul score final
    score -= validationErrors.reduce((total, error) => {
      return total + this.getErrorWeight(error.severity);
    }, 0);
    
    return {
      valid: validationErrors.filter(e => e.severity === 'high').length === 0,
      score: Math.max(score, 0),
      message: this.generateValidationMessage(validationErrors),
      suggestions: this.generateArchitectureSuggestions(content, validationErrors)
    };
  }
  
  private validateRequirementsAlignment(
    designContent: string,
    requirements: string
  ): ValidationError[] {
    const errors: ValidationError[] = [];
    
    // Extraction user stories des requirements
    const userStories = this.extractUserStories(requirements);
    
    // Vérification que chaque user story a une correspondance design
    for (const story of userStories) {
      const hasDesignCorrespondence = this.findDesignElements(
        designContent, story.functionalArea
      );
      
      if (!hasDesignCorrespondence) {
        errors.push({
          type: 'missing_design_for_requirement',
          message: `Aucun élément design trouvé pour: ${story.title}`,
          severity: 'high',
          suggestion: `Ajoutez composants/architecture pour supporter: ${story.description}`
        });
      }
    }
    
    return errors;
  }
}
```

#### US-020 : Tasks Validator (8 points)
**EN TANT QUE** serveur MCP  
**JE VEUX** valider faisabilité et atomicité des tâches  
**AFIN D'** assurer planning exécutable

**Acceptance Criteria :**
- Validation format tâches (titre, description, estimation)
- Vérification dépendances cohérentes et acycliques
- Estimation réaliste vs complexité détectée
- Atomicité tâches (objectif SMART)
- Suggestions décomposition si tâches trop complexes

#### US-021 : Rule Engine Configurable (5 points)
**EN TANT QUE** administrateur système  
**JE VEUX** configurer règles validation métier  
**AFIN D'** adapter validation aux standards équipe

**Acceptance Criteria :**
- Configuration JSON/YAML des règles par phase
- Rules engine dynamique avec hot reload
- Règles personnalisées JavaScript sandboxées
- Interface admin pour gestion règles
- Versioning règles avec rollback

### PLANNING DÉTAILLÉ SPRINT 2

#### SEMAINE 10
**Lundi :** Sprint Planning + Architecture validation (Tech Lead)
- Planning Sprint 2 avec focus validation métier
- Architecture Rule Engine extensible
- Patterns validation configurables

**Mardi-Mercredi :** Requirements Validator (Développeur Senior)
- Implémentation validation user stories
- Critères acceptation format EARS
- Tests validation avec spécifications réelles

**Jeudi-Vendredi :** Design Validator (Tech Lead + Développeur Backend)
- Validation cohérence architecture
- Alignment avec requirements et tech spec
- Détection anti-patterns architecture

#### SEMAINE 11
**Lundi :** Tasks Validator (Développeur Backend)
- Validation atomicité et dépendances
- Estimation vs complexité
- Suggestions décomposition

**Mardi :** Rule Engine configuration (Tech Lead + Développeur Senior)
- Système configuration règles
- Hot reload et versioning
- Interface admin basique

**Mercredi :** Tests intégration validation (Équipe complète)
- Tests end-to-end validation métier
- Performance et edge cases
- Integration avec outils MCP

**Jeudi :** Remplacement stubs par validateurs réels (Développeur Senior)
- Migration sdd_validate_* tools
- Tests regression compatibilité
- Documentation API mise à jour

**Vendredi :** Sprint Review/Demo validation (Product Owner + équipe)
- Démonstration validation métier fonctionnelle
- Comparaison avec stubs Phase 1
- Validation acceptance criteria

### TESTS CRITIQUES SPRINT 2

#### Tests Validation Métier
```typescript
describe('Requirements Validation', () => {
  test('validates proper user story format', async () => {
    const validUserStory = `
# User Stories

**US-001**: En tant qu'utilisateur, je veux me connecter, afin d'accéder à mes projets.

**Critères d'acceptation**:
- QUAND l'utilisateur saisit email/mot de passe valides ALORS connexion réussie
- QUAND l'utilisateur saisit données invalides ALORS message d'erreur affiché
    `;
    
    const result = await requirementsValidator.validate(validUserStory, context);
    
    expect(result.valid).toBe(true);
    expect(result.score).toBeGreaterThan(0.8);
    expect(result.details.userStoriesCount).toBe(1);
    expect(result.details.acceptanceCriteriaCount).toBe(2);
  });
  
  test('detects invalid user story format', async () => {
    const invalidUserStory = `
# User Stories

**US-001**: Connexion utilisateur simple
    `;
    
    const result = await requirementsValidator.validate(invalidUserStory, context);
    
    expect(result.valid).toBe(false);
    expect(result.details.errors).toContainEqual(
      expect.objectContaining({
        type: 'invalid_user_story_format'
      })
    );
  });
});
```

### LIVRABLES SPRINT 2
- [x] 3 validateurs métier fonctionnels (Requirements, Design, Tasks)
- [x] Rule Engine configurable avec hot reload
- [x] Remplacement stubs par validation réelle
- [x] Tests validation exhaustifs avec cas edge
- [x] Performance validation <100ms confirmée

---

## SPRINT 3 - PROMPT ORCHESTRATOR (Semaine 12)

### SPRINT GOAL
"Génération de prompts contextuels intelligents"

### PRÉREQUIS
Sprint 2 completé avec validation métier opérationnelle

### USER STORIES DÉTAILLÉES

#### US-022 : Moteur Template Handlebars (8 points)
**EN TANT QUE** serveur MCP  
**JE VEUX** générer prompts dynamiques avec contexte  
**AFIN D'** optimiser interaction LLM selon projet

**Acceptance Criteria :**
- Templates Handlebars avec helpers personnalisés SDD
- Contexte intelligent (historique, préférences, projet)
- Cache templates compilés pour performance
- Versioning templates avec migration automatique
- Hot reload templates pour développement

**Architecture Prompt Orchestrator :**
```typescript
// src/prompts/PromptOrchestrator.ts
export class PromptOrchestrator {
  private templateCache = new Map<string, CompiledTemplate>();
  private contextProvider: ContextProvider;
  
  constructor(
    private templateEngine: HandlebarsTemplateEngine,
    private stateRepository: IStateRepository,
    private userPreferences: UserPreferencesService
  ) {
    this.registerSddHelpers();
  }
  
  async generatePrompt(
    templateName: string,
    projectId: string,
    additionalContext?: Record<string, any>
  ): Promise<GeneratedPrompt> {
    // Compilation template avec cache
    const template = await this.getCompiledTemplate(templateName);
    
    // Construction contexte intelligent
    const context = await this.buildPromptContext(projectId, additionalContext);
    
    // Génération prompt
    const renderedPrompt = template(context);
    
    // Post-processing et optimisation
    const optimizedPrompt = await this.optimizePrompt(renderedPrompt, context);
    
    return {
      prompt: optimizedPrompt,
      context,
      templateName,
      generatedAt: new Date(),
      tokens: this.estimateTokens(optimizedPrompt)
    };
  }
  
  private async buildPromptContext(
    projectId: string,
    additionalContext?: Record<string, any>
  ): Promise<PromptContext> {
    const project = await this.stateRepository.getProject(projectId);
    if (!project) throw new Error(`Project not found: ${projectId}`);
    
    const specifications = await this.stateRepository.listSpecifications(projectId);
    const history = await this.stateRepository.getProjectHistory(projectId);
    const userPrefs = await this.userPreferences.getPreferences(project.ownerId);
    
    return {
      // Contexte projet
      project: {
        id: project.id,
        name: project.name,
        description: project.description,
        currentPhase: project.currentPhase,
        createdAt: project.createdAt,
        lastModified: project.updatedAt
      },
      
      // Spécifications disponibles
      specifications: specifications.map(spec => ({
        phase: spec.phase,
        content: this.summarizeContent(spec.content),
        lastModified: spec.createdAt
      })),
      
      // Historique récent pertinent
      recentHistory: history
        .slice(0, 10)
        .map(entry => ({
          action: entry.action,
          timestamp: entry.timestamp,
          summary: this.summarizeHistoryEntry(entry)
        })),
      
      // Préférences utilisateur
      preferences: {
        language: userPrefs.language || 'fr',
        verbosity: userPrefs.verbosity || 'medium',
        focusAreas: userPrefs.focusAreas || [],
        avoidedPatterns: userPrefs.avoidedPatterns || []
      },
      
      // Contexte additionnel
      ...additionalContext,
      
      // Helpers dynamiques
      helpers: {
        formatDate: (date: Date) => date.toLocaleDateString('fr-FR'),
        truncate: (text: string, length: number) => 
          text.length > length ? text.slice(0, length) + '...' : text,
        phaseTranslation: (phase: string) => this.translatePhase(phase),
        estimateReadingTime: (content: string) => Math.ceil(content.length / 1000)
      }
    };
  }
  
  private registerSddHelpers(): void {
    // Helper phase progression
    this.templateEngine.registerHelper('nextPhases', (currentPhase: string) => {
      const transitions = VALID_TRANSITIONS[currentPhase as SDDPhase] || [];
      return transitions.map(phase => this.translatePhase(phase));
    });
    
    // Helper validation status
    this.templateEngine.registerHelper('validationStatus', (content: string, phase: string) => {
      // Simulation validation rapide pour prompts
      return content.length > 100 ? 'Acceptable' : 'Insuffisant';
    });
    
    // Helper suggestions contextuelles
    this.templateEngine.registerHelper('phaseSuggestions', (phase: string, project: any) => {
      return this.getPhaseSpecificSuggestions(phase, project);
    });
  }
}
```

#### US-023 : Templates SDD Contextuels (5 points)
**EN TANT QUE** LLM client  
**JE VEUX** recevoir prompts adaptés au contexte projet  
**AFIN D'** produire réponses plus pertinentes

**Acceptance Criteria :**
- Templates spécialisés par phase SDD
- Injection automatique contexte projet
- Adaptation selon historique et préférences
- Optimisation longueur prompt vs pertinence
- Support multi-langue avec templates localisés

**Templates Handlebars SDD :**
```handlebars
{{!-- templates/requirements-analysis.hbs --}}
# Analyse des Exigences - {{project.name}}

## Contexte du Projet
Le projet "{{project.name}}" est actuellement en phase **{{phaseTranslation project.currentPhase}}**.
{{#if project.description}}
Description: {{project.description}}
{{/if}}

## Objectif de l'Analyse
Vous devez analyser et améliorer les exigences suivantes selon les standards EARS et bonnes pratiques SDD.

{{#if specifications}}
## Spécifications Existantes
{{#each specifications}}
### {{phaseTranslation this.phase}} ({{formatDate this.lastModified}})
{{truncate this.content 200}}
{{/each}}
{{/if}}

## Critères de Qualité
- User stories au format: "En tant que [rôle], je veux [action], afin de [bénéfice]"
- Critères acceptation format EARS: "QUAND [condition] ALORS [résultat]"
- Exigences complètes, cohérentes et testables
{{#if preferences.focusAreas}}
- Focus particulier sur: {{#each preferences.focusAreas}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}

## Progression Suggérée
{{#if (nextPhases project.currentPhase)}}
Phases suivantes possibles: {{#each (nextPhases project.currentPhase)}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
{{/if}}

{{#if recentHistory}}
## Historique Récent
{{#each recentHistory}}
- {{formatDate this.timestamp}}: {{this.summary}}
{{/each}}
{{/if}}

---
Analysez le contenu et proposez améliorations spécifiques avec exemples concrets.
```

#### US-024 : Context Intelligence (3 points)
**EN TANT QUE** Prompt Orchestrator  
**JE VEUX** analyser contexte projet pour optimiser prompts  
**AFIN D'** fournir assistance la plus pertinente

**Acceptance Criteria :**
- Analyse automatique complexité projet
- Détection patterns et recommandations
- Adaptation style selon préférences utilisateur
- Mémorisation contexte conversation
- Suggestions proactives selon phase

### PLANNING DÉTAILLÉ SPRINT 3

#### SEMAINE 12
**Lundi :** Sprint Planning + Architecture Prompt Orchestrator (Tech Lead)
- Planning Sprint 3 finalisation Phase 2
- Architecture templates Handlebars
- Système contexte intelligent

**Mardi :** Moteur Template Handlebars (Développeur Backend)
- Implémentation PromptOrchestrator
- Helpers SDD personnalisés
- Cache et performance

**Mercredi :** Templates SDD contextuels (Développeur Backend + Product Owner)
- Création templates par phase
- Tests génération avec projets réels
- Optimisation prompts LLM

**Jeudi :** Context Intelligence + tests (Tech Lead + Développeur Backend)
- Logique contexte intelligent
- Tests intégration complète
- Performance et edge cases

**Vendredi :** Sprint Review/Demo Phase 2 + rétrospective (Équipe complète)
- Démonstration Phase 2 complète
- Validation finale Product Owner
- Rétrospective et préparation Phase 3

### TESTS FINAUX PHASE 2

#### Tests Prompt Orchestrator
```typescript
describe('Prompt Orchestrator', () => {
  test('generates contextual prompts for different phases', async () => {
    const projectId = 'test-project-123';
    
    // Test requirements phase
    const reqPrompt = await orchestrator.generatePrompt(
      'requirements-analysis', projectId
    );
    
    expect(reqPrompt.prompt).toContain('phase requirements');
    expect(reqPrompt.context.project.currentPhase).toBe('requirements');
    expect(reqPrompt.tokens).toBeLessThan(4000); // Limite tokens
    
    // Test design phase
    const designPrompt = await orchestrator.generatePrompt(
      'design-review', projectId
    );
    
    expect(designPrompt.prompt).toContain('architecture');
    expect(designPrompt.context.specifications).toHaveLength(2);
  });
});
```

### LIVRABLES FINAUX PHASE 2

#### Fonctionnalités Consolidées
- [x] **Migration PostgreSQL** transparente sans perte données
- [x] **Validation métier réelle** remplaçant 100% des stubs
- [x] **Prompt Orchestrator** avec templates contextuels
- [x] **Performance optimisée** ≥ 2x amélioration vs Phase 1

#### Infrastructure Robuste
- [x] **PostgreSQL** avec schéma optimisé et indexes
- [x] **Scripts migration** automatiques avec rollback
- [x] **Rule Engine** configurable pour validation
- [x] **Tests automatisés** maintiennent >85% coverage

#### Architecture Évolutive
- [x] **Interfaces stables** pour migration Phase 3 (Redis, JWT)
- [x] **Validation extensible** avec règles configurables
- [x] **Templates dynamiques** avec contexte intelligent
- [x] **Documentation complète** migration et opérations

### TRANSITION VERS PHASE 3

#### Préparation Production
- Architecture prête pour ajout sécurité JWT et cache Redis
- Performance baseline établie pour optimisation Phase 3
- Monitoring hooks préparés pour stack Prometheus
- Documentation opérationnelle complète

#### Roadmap Phase 3
- Ajout sécurité JWT avec autorisation granulaire
- Cache Redis multi-layer pour performance
- Stack monitoring Prometheus/Grafana complet
- sdd_execute_task avec intégration CI/CD réelle

---

## MÉTRIQUES GLOBALES PHASE 2

### Métriques de Migration
- **Intégrité données** : 100% migration sans perte
- **Performance** : ≥ 2x amélioration PostgreSQL vs fichiers
- **Compatibilité** : Zero breaking changes pour clients MCP
- **Fiabilité** : Scripts migration testés sur datasets variés

### Métriques Validation
- **Couverture** : 100% stubs remplacés par validation réelle
- **Précision** : Validation métier conforme standards industrie
- **Performance** : <100ms validation pour spécifications standard
- **Configurabilité** : Rule engine extensible et versionné

### Métriques Innovation
- **Prompts intelligents** : Contexte dynamique selon projet
- **Templates adaptatifs** : Personnalisation selon utilisateur
- **Performance optimisée** : Cache et requêtes optimisées
- **Architecture évolutive** : Ready for Phase 3 production

---

## CONCLUSION PHASE 2

La Phase 2 Consolidation transforme le MVP fonctionnel en solution robuste avec persistence PostgreSQL, validation métier réelle et génération de prompts intelligents. L'architecture évolutive et les interfaces stables garantissent une migration transparente vers la Phase 3 Production avec sécurité, monitoring et intégrations avancées.

**READY FOR PHASE 3 - PRODUCTION**
