# PHASE 1 - MVP DÉTAILLÉE (3 SPRINTS)
*Plan d'exécution opérationnel - Semaines 2-7*

## VUE D'ENSEMBLE PHASE 1

### OBJECTIF GLOBAL
Créer un serveur MCP fonctionnel avec workflow SDD complet end-to-end, déployable et utilisable par les équipes de développement.

### ARCHITECTURE CIBLE PHASE 1

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP Client    │────│  JSON-RPC 2.0   │────│ Workflow Manager│
│     (LLM)       │    │     Server      │    │  (Core Logic)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                │                       │
                        ┌─────────────────┐    ┌─────────────────┐
                        │ Template Engine │    │ State Manager   │
                        │   (Basique)     │    │   (Fichiers)    │
                        └─────────────────┘    └─────────────────┘
```

### ROADMAP 3 SPRINTS

```
Sprint 1         Sprint 2         Sprint 3
(Sem 2-3)       (Sem 4-5)        (Sem 6-7)
CORE ENGINE     TOOLS MCP        FINALISATION
│               │                │
├─ MCP Server   ├─ sdd_project_*  ├─ Validation stubs
├─ State Mgr    ├─ sdd_spec_*     ├─ Documentation
├─ Workflow     ├─ sdd_phase_*    ├─ Docker deploy
└─ Tests        ├─ Template Eng   └─ Tests e2e
                └─ Tests integ
```

### ÉQUIPE ET RESPONSABILITÉS

**Tech Lead** : Architecture, Core Engine, validation technique
**Développeur Senior** : Implémentation outils MCP, State Manager
**DevOps Engineer** : Tests, CI/CD, déploiement, monitoring
**Product Owner** : Validation métier, acceptance criteria, documentation

---

## SPRINT 1 - CORE ENGINE (Semaines 2-3)

### SPRINT GOAL
"Fondations MCP solides avec workflow SDD basique"

### PRÉREQUIS
Phase 0 complétée avec infrastructure, architecture et équipe prêtes

### USER STORIES DÉTAILLÉES

#### US-001 : Serveur MCP JSON-RPC 2.0 (8 points)
**EN TANT QUE** développeur utilisant un client MCP  
**JE VEUX** pouvoir me connecter au serveur MCP SDD  
**AFIN DE** découvrir et utiliser les outils disponibles

**Acceptance Criteria :**
- Serveur écoute sur port 3000 configurable via variable environnement
- Répond à `tools/list` avec format JSON-RPC 2.0 correct
- Gère les erreurs standards MCP (-32601, -32602, -32603)
- Logs structurés avec Winston pour debugging et monitoring
- Health check endpoint `/health` fonctionnel pour monitoring

**Implémentation Technique :**
```typescript
// src/core/MCPServer.ts
export class MCPServer {
  private express: Express;
  private toolRegistry: MCPToolRegistry;
  
  constructor(config: ServerConfig) {
    this.express = express();
    this.setupMiddleware();
    this.setupRoutes();
  }
  
  async handleJSONRPC(request: JSONRPCRequest): Promise<JSONRPCResponse> {
    const logger = this.getRequestLogger(request.id);
    
    try {
      switch (request.method) {
        case 'tools/list':
          return await this.handleToolsList(request);
        case 'tools/call':
          return await this.handleToolsCall(request);
        default:
          return this.createErrorResponse(request.id, -32601, 'Method not found');
      }
    } catch (error) {
      logger.error('JSONRPC execution failed', { error, request });
      return this.createErrorResponse(request.id, -32603, 'Internal error');
    }
  }
}
```

#### US-002 : State Manager Fichiers (13 points)
**EN TANT QUE** serveur MCP  
**JE VEUX** pouvoir persister l'état des projets SDD  
**AFIN DE** maintenir les données entre redémarrages

**Acceptance Criteria :**
- Interface `IStateRepository` implémentée avec toutes méthodes
- Persistence dans fichiers JSON avec structure `/data/projects/{uuid}/`
- Opérations CRUD projets complètes (create, read, update, delete, list)
- Gestion erreurs robuste (fichier corrompu, permissions, espace disque)
- Thread-safe pour accès concurrent avec file locking

**Structure de Données :**
```typescript
// Structure fichier projet
/data/projects/
├── {project-uuid}/
│   ├── metadata.json      # Informations projet
│   ├── state.json         # État workflow
│   ├── requirements.md    # Spécifications exigences
│   ├── design.md         # Spécifications conception
│   ├── tasks.md          # Décomposition tâches
│   └── logs/             # Historique modifications
│       ├── 2024-01-01.log
│       └── 2024-01-02.log

// Interface IStateRepository
export interface IStateRepository {
  // Project CRUD
  createProject(project: ProjectCreate): Promise<Project>;
  getProject(id: string): Promise<Project | null>;
  updateProject(id: string, updates: ProjectUpdate): Promise<Project>;
  listProjects(): Promise<Project[]>;
  deleteProject(id: string): Promise<void>;
  
  // Specification management
  saveSpecification(projectId: string, phase: SDDPhase, content: string): Promise<void>;
  getSpecification(projectId: string, phase: SDDPhase): Promise<string | null>;
  listSpecifications(projectId: string): Promise<Array<{phase: SDDPhase, content: string}>>;
  
  // History and audit
  appendLog(projectId: string, action: string, metadata: object): Promise<void>;
  getProjectHistory(projectId: string): Promise<LogEntry[]>;
}
```

#### US-003 : Workflow Manager (8 points)
**EN TANT QUE** serveur MCP  
**JE VEUX** gérer les transitions de phases SDD  
**AFIN DE** garantir la cohérence du workflow

**Acceptance Criteria :**
- Machine à états phases SDD (NEW → REQUIREMENTS → DESIGN → TASKS → EXECUTION → COMPLETED)
- Validation transitions autorisées selon matrice définie
- Interface `IWorkflowManager` respectée avec toutes méthodes
- Persistence état workflow dans State Manager
- Logs transitions avec timestamp pour audit et debugging

**Machine à États :**
```typescript
// Phases SDD
export enum SDDPhase {
  NEW = 'new',
  REQUIREMENTS = 'requirements', 
  DESIGN = 'design',
  TASKS = 'tasks',
  EXECUTION = 'execution',
  COMPLETED = 'completed'
}

// Transitions autorisées
const VALID_TRANSITIONS = {
  [SDDPhase.NEW]: [SDDPhase.REQUIREMENTS],
  [SDDPhase.REQUIREMENTS]: [SDDPhase.DESIGN, SDDPhase.NEW],
  [SDDPhase.DESIGN]: [SDDPhase.TASKS, SDDPhase.REQUIREMENTS],
  [SDDPhase.TASKS]: [SDDPhase.EXECUTION, SDDPhase.DESIGN],
  [SDDPhase.EXECUTION]: [SDDPhase.COMPLETED, SDDPhase.TASKS],
  [SDDPhase.COMPLETED]: [] // Terminal state
};

// Interface IWorkflowManager
export interface IWorkflowManager {
  validateTransition(from: SDDPhase, to: SDDPhase): boolean;
  executeTransition(projectId: string, to: SDDPhase): Promise<void>;
  getCurrentPhase(projectId: string): Promise<SDDPhase>;
  getAvailableTransitions(currentPhase: SDDPhase): Promise<SDDPhase[]>;
  getPhaseRequirements(phase: SDDPhase): Promise<string[]>;
}
```

### PLANNING DÉTAILLÉ SPRINT 1

#### SEMAINE 2
**Lundi :** Sprint Planning (2h) + Setup US-001 architecture (Tech Lead)
- Sprint Planning avec équipe complète
- Revue Definition of Done et acceptance criteria
- Setup architecture Core MCP Engine

**Mardi-Mercredi :** Core MCP Engine + JSON-RPC (Tech Lead + Développeur Senior)
- Implémentation serveur Express + middleware
- Handler JSON-RPC 2.0 avec validation format
- Gestion erreurs standards MCP
- Tests unitaires Core Engine

**Jeudi-Vendredi :** State Manager fichiers (Développeur Senior)
- Implémentation interface IStateRepository
- Système fichiers avec structure /projects/{uuid}/
- Gestion erreurs et thread safety
- Tests unitaires State Manager

#### SEMAINE 3
**Lundi-Mardi :** Workflow Manager (Tech Lead + Développeur Senior)
- Implémentation machine à états SDD
- Validation transitions et persistence
- Interface IWorkflowManager complète
- Tests unitaires Workflow Manager

**Mercredi :** Tests intégration (Équipe complète)
- Tests intégration Core Engine + State Manager + Workflow
- Tests JSON-RPC 2.0 compliance
- Tests charge et performance basiques

**Jeudi :** Debugging et fixes (Équipe complète)
- Résolution bugs identifiés
- Optimisation performance
- Revue code et refactoring

**Vendredi :** Sprint Review/Demo (Équipe + Product Owner)
- Démonstration fonctionnalités implémentées
- Validation acceptance criteria
- Rétrospective Sprint 1

### TESTS CRITIQUES SPRINT 1

#### Tests Unitaires (>90% coverage)
```typescript
// Core MCP Engine tests
describe('MCPServer', () => {
  test('handles tools/list correctly', async () => {
    const response = await server.handleJSONRPC({
      jsonrpc: '2.0', id: 1, method: 'tools/list'
    });
    expect(response.result.tools).toEqual([]);
  });
  
  test('returns method not found for invalid method', async () => {
    const response = await server.handleJSONRPC({
      jsonrpc: '2.0', id: 1, method: 'invalid/method'
    });
    expect(response.error.code).toBe(-32601);
  });
});

// State Manager tests
describe('FileStateRepository', () => {
  test('creates project with valid UUID', async () => {
    const project = await repo.createProject({
      name: 'Test Project',
      description: 'Test description'
    });
    expect(project.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
  });
  
  test('handles file corruption gracefully', async () => {
    // Simulate corrupted file
    await fs.writeFile(getProjectPath(projectId), 'invalid json');
    const project = await repo.getProject(projectId);
    expect(project).toBeNull();
  });
});
```

#### Tests Intégration JSON-RPC 2.0
```typescript
describe('MCP Protocol Compliance', () => {
  test('complete tools/list workflow', async () => {
    const client = new MCPTestClient('http://localhost:3000');
    const response = await client.request('tools/list', {});
    
    expect(response).toMatchSchema(jsonRpcResponseSchema);
    expect(response.result.tools).toBeArray();
  });
  
  test('error responses follow JSON-RPC format', async () => {
    const response = await client.request('invalid/method', {});
    expect(response.error.code).toBe(-32601);
    expect(response.error.message).toBe('Method not found');
  });
});
```

### LIVRABLES SPRINT 1
- [x] Serveur MCP fonctionnel répondant aux requêtes tools/list
- [x] State Manager avec persistence fichiers JSON thread-safe
- [x] Workflow Manager avec machine à états SDD complète
- [x] Tests automatisés avec >90% coverage
- [x] Documentation technique architecture mise à jour

---

## SPRINT 2 - OUTILS MCP PRIORITAIRES (Semaines 4-5)

### SPRINT GOAL
"Outils MCP fonctionnels pour workflow SDD complet"

### PRÉREQUIS
Sprint 1 completé avec Core Engine, State Manager et Workflow Manager opérationnels

### USER STORIES DÉTAILLÉES

#### US-004 : sdd_project_init (8 points)
**EN TANT QUE** utilisateur MCP  
**JE VEUX** pouvoir initialiser un nouveau projet SDD  
**AFIN DE** commencer le workflow de développement

**Acceptance Criteria :**
- Crée structure projet avec UUID unique généré automatiquement
- Initialise état NEW dans Workflow Manager
- Génère structure dossiers et fichiers base selon template
- Retourne ID projet et état initial dans format JSON standard
- Gestion erreurs complète (nom invalide, projet existant)

**Schéma Input/Output :**
```typescript
// Input Schema
{
  "type": "object",
  "properties": {
    "name": {"type": "string", "minLength": 3, "maxLength": 100},
    "description": {"type": "string", "maxLength": 500},
    "metadata": {"type": "object"}
  },
  "required": ["name"]
}

// Output Format
{
  "project": {
    "id": "uuid",
    "name": "string",
    "description": "string",
    "currentPhase": "new",
    "createdAt": "ISO 8601",
    "structure": {
      "created_files": ["requirements.md", "design.md", "tasks.md"],
      "created_folders": ["logs", "artifacts"]
    }
  }
}
```

#### US-005 : sdd_project_status (5 points)
**EN TANT QUE** utilisateur MCP  
**JE VEUX** connaître l'état d'avancement d'un projet  
**AFIN DE** comprendre la phase courante et prochaines étapes

**Acceptance Criteria :**
- Affiche phase courante et transitions disponibles
- Montre artefacts existants par phase avec tailles et dates
- Indique dernière modification et historique récent
- Format JSON structuré et facile à lire
- Gestion projet inexistant avec message explicite

**Output Détaillé :**
```json
{
  "project": {
    "id": "uuid",
    "name": "string",
    "currentPhase": "requirements",
    "lastModified": "ISO 8601",
    "availableTransitions": ["design", "new"],
    "artifacts": {
      "requirements": {"exists": true, "size": 1024, "lastModified": "ISO 8601"},
      "design": {"exists": false},
      "tasks": {"exists": false}
    },
    "recentHistory": [
      {"timestamp": "ISO 8601", "action": "phase_transition", "from": "new", "to": "requirements"}
    ]
  }
}
```

#### US-006 : sdd_spec_create (13 points)
**EN TANT QUE** utilisateur MCP  
**JE VEUX** créer/éditer des spécifications par phase  
**AFIN DE** documenter exigences, conception ou tâches

**Acceptance Criteria :**
- Support phases requirements, design, tasks avec validation spécifique
- Intégration Template Engine pour structure et guidance
- Validation contenu selon phase (format markdown, sections requises)
- Sauvegarde dans State Manager avec versioning automatique
- Backup automatique avant modification pour rollback

**Validation par Phase :**
```typescript
// Requirements validation
const requirementsValidation = {
  requiredSections: ['Introduction', 'User Stories', 'Acceptance Criteria'],
  userStoryFormat: /En tant que .+, je veux .+, afin de .+/,
  acceptanceCriteriaFormat: /QUAND .+ ALORS .+/
};

// Design validation  
const designValidation = {
  requiredSections: ['Architecture', 'Components', 'Data Models'],
  diagramsRequired: true,
  techStackAlignment: true
};

// Tasks validation
const tasksValidation = {
  requiredSections: ['Task Breakdown', 'Dependencies', 'Estimates'],
  taskFormat: /^T-\d+: .+/,
  estimationRequired: true
};
```

#### US-007 : sdd_phase_advance (8 points)
**EN TANT QUE** utilisateur MCP  
**JE VEUX** faire progresser un projet vers la phase suivante  
**AFIN DE** suivre le workflow SDD structuré

**Acceptance Criteria :**
- Validation transition via Workflow Manager avec règles métier
- Vérification prérequis phase courante (artefacts requis)
- Mise à jour état projet avec timestamp et metadata
- Logs transition détaillés pour audit et traçabilité
- Messages erreur explicites si blocage avec suggestions

**Prérequis par Phase :**
```typescript
const phasePrerequisites = {
  [SDDPhase.REQUIREMENTS]: {
    artifacts: ['requirements.md'],
    validation: 'basic_format_check',
    minimumContent: 500 // caractères
  },
  [SDDPhase.DESIGN]: {
    artifacts: ['requirements.md', 'design.md'],
    validation: 'requirements_validated',
    minimumContent: 1000
  },
  [SDDPhase.TASKS]: {
    artifacts: ['requirements.md', 'design.md', 'tasks.md'],
    validation: 'design_validated',
    minimumContent: 800
  }
};
```

#### US-008 : Template Engine (8 points)
**EN TANT QUE** serveur MCP  
**JE VEUX** fournir des templates structurés par phase  
**AFIN DE** guider la création de spécifications

**Acceptance Criteria :**
- Templates requirements, design, tasks disponibles et à jour
- Chargement depuis dossier `/templates/` avec hot reload
- Variables substitution basique (nom projet, date, etc.)
- Cache templates compilés pour performance optimale
- Gestion template manquant/corrompu avec fallback

**Templates Structure :**
```markdown
# templates/requirements-template.md
# Spécification d'Exigences - {{projectName}}

## 1. Introduction et Vision
Cette spécification définit les exigences pour {{projectName}}, créé le {{createdDate}}.

## 2. Exigences Fonctionnelles

### User Stories
**US-001**: En tant que [rôle], je veux [fonctionnalité], afin de [bénéfice].

**Critères d'acceptation**:
- QUAND [condition] ALORS [résultat attendu]
- QUAND [condition] ALORS [résultat attendu]

## 3. Exigences Non Fonctionnelles
[Performance, sécurité, utilisabilité, etc.]
```

### PLANNING DÉTAILLÉ SPRINT 2

#### SEMAINE 4
**Lundi :** Sprint Planning (2h) + Architecture outils MCP (Tech Lead)
- Review Sprint 1 deliverables et transition
- Architecture pattern outils MCP standardisée
- Setup registry et interfaces communes

**Mardi :** sdd_project_init + sdd_project_status (Développeur Senior)
- Implémentation sdd_project_init avec génération UUID
- Implémentation sdd_project_status avec formatting JSON
- Tests unitaires et validation schémas

**Mercredi :** Template Engine (Tech Lead)
- Système chargement templates avec cache
- Variables substitution et compilation
- Hot reload pour développement

**Jeudi :** sdd_spec_create début (Développeur Senior + Tech Lead)
- Architecture édition spécifications
- Intégration Template Engine
- Validation contenu par phase

**Vendredi :** sdd_spec_create fin + tests (Développeur Senior)
- Finalisation sdd_spec_create avec versioning
- Tests unitaires complets
- Tests intégration Template Engine

#### SEMAINE 5
**Lundi :** sdd_phase_advance (Tech Lead + Développeur Senior)
- Logique validation prérequis
- Intégration Workflow Manager
- Tests transitions valides/invalides

**Mardi :** Tests intégration 4 outils (Équipe complète)
- Tests intégration entre tous outils
- Validation workflow complet
- Tests performance et charge

**Mercredi :** Tests end-to-end workflow complet (DevOps + équipe)
- Scénarios utilisateur complets
- Tests compatibilité clients MCP
- Automatisation tests e2e

**Jeudi :** Debugging, fixes et optimisation (Équipe complète)
- Résolution bugs et optimisations
- Profiling performance
- Revue code et refactoring

**Vendredi :** Sprint Review/Demo workflow SDD (Product Owner + équipe)
- Démonstration workflow SDD complet
- Validation acceptance criteria
- Feedback Product Owner et ajustements

### ARCHITECTURE OUTILS MCP STANDARDISÉE

```typescript
// Pattern standard pour tous outils MCP
export abstract class BaseMCPTool implements IMCPToolHandler {
  abstract name: string;
  abstract description: string;
  abstract inputSchema: object;
  
  constructor(
    protected stateRepository: IStateRepository,
    protected workflowManager: IWorkflowManager,
    protected logger: Logger
  ) {}
  
  async execute(params: any, context?: MCPContext): Promise<MCPToolResult> {
    const requestId = context?.requestId || generateId();
    
    try {
      // Validation input commune
      this.validateInput(params);
      
      // Logs début traitement
      this.logger.info('Tool execution started', {
        tool: this.name,
        requestId,
        params: this.sanitizeParams(params)
      });
      
      // Exécution spécifique outil
      const result = await this.executeInternal(params, context);
      
      // Logs succès
      this.logger.info('Tool execution completed', {
        tool: this.name,
        requestId,
        duration: Date.now() - startTime
      });
      
      return result;
      
    } catch (error) {
      // Gestion erreurs standardisée
      this.logger.error('Tool execution failed', {
        tool: this.name,
        requestId,
        error: error.message,
        stack: error.stack
      });
      
      return {
        content: [{
          type: 'text',
          text: `Erreur lors de l'exécution de ${this.name}: ${error.message}`
        }],
        isError: true
      };
    }
  }
  
  protected abstract executeInternal(params: any, context?: MCPContext): Promise<MCPToolResult>;
  protected abstract validateInput(params: any): void;
  protected abstract sanitizeParams(params: any): any;
}

// Registry centralise avec hot reload
export class MCPToolRegistry {
  private tools: Map<string, IMCPToolHandler> = new Map();
  
  register(tool: IMCPToolHandler): void {
    this.tools.set(tool.name, tool);
    this.logger.info('Tool registered', { toolName: tool.name });
  }
  
  async execute(toolName: string, params: any, context?: MCPContext): Promise<MCPToolResult> {
    const tool = this.tools.get(toolName);
    if (!tool) {
      throw new Error(`Tool not found: ${toolName}`);
    }
    
    return tool.execute(params, context);
  }
  
  listTools(): Array<{name: string, description: string, inputSchema: object}> {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      description: tool.description,
      inputSchema: tool.inputSchema
    }));
  }
}
```

### TESTS CRITIQUES SPRINT 2

#### Tests Intégration Workflow Complet
```typescript
describe('SDD Workflow End-to-End', () => {
  test('complete workflow NEW → REQUIREMENTS → DESIGN → TASKS', async () => {
    // 1. Initialize project
    const initResult = await toolRegistry.execute('sdd_project_init', {
      name: 'Test Project E2E',
      description: 'End-to-end test project'
    });
    const projectId = initResult.content[0].data.project.id;
    
    // 2. Check initial status
    const statusResult = await toolRegistry.execute('sdd_project_status', { projectId });
    expect(statusResult.content[0].data.project.currentPhase).toBe('new');
    
    // 3. Create requirements
    await toolRegistry.execute('sdd_spec_create', {
      projectId,
      phase: 'requirements',
      content: validRequirementsContent
    });
    
    // 4. Advance to requirements phase
    await toolRegistry.execute('sdd_phase_advance', {
      projectId,
      targetPhase: 'requirements'
    });
    
    // 5. Create design
    await toolRegistry.execute('sdd_spec_create', {
      projectId,
      phase: 'design', 
      content: validDesignContent
    });
    
    // 6. Advance to design phase
    await toolRegistry.execute('sdd_phase_advance', {
      projectId,
      targetPhase: 'design'
    });
    
    // 7. Verify final status
    const finalStatus = await toolRegistry.execute('sdd_project_status', { projectId });
    expect(finalStatus.content[0].data.project.currentPhase).toBe('design');
  });
});
```

### LIVRABLES SPRINT 2
- [x] 4 outils MCP prioritaires implémentés et testés
- [x] Template Engine fonctionnel avec templates SDD
- [x] Workflow SDD end-to-end opérationnel  
- [x] Tests automatisés robustes (unitaires + intégration + e2e)
- [x] Démonstration fonctionnelle validée Product Owner

---

## SPRINT 3 - FINALISATION MVP (Semaines 6-7)

### SPRINT GOAL
"MVP complet déployable avec workflow SDD end-to-end"

### PRÉREQUIS
Sprint 2 completé avec 4 outils MCP fonctionnels et Template Engine

### USER STORIES DÉTAILLÉES

#### US-009 : sdd_validate_requirements (Stub) (3 points)
**EN TANT QUE** serveur MCP  
**JE VEUX** valider la qualité des exigences  
**AFIN DE** permettre la progression vers la phase design

**Acceptance Criteria :**
- Retourne toujours `{"valid": true, "message": "Validation basique OK"}`
- Interface compatible pour migration Phase 2 sans breaking changes
- Logs appel pour métriques et monitoring usage
- Documentation stub explicite avec roadmap Phase 2

#### US-010 : sdd_validate_design (Stub) (3 points)
**EN TANT QUE** serveur MCP  
**JE VEUX** valider la cohérence de la conception  
**AFIN DE** permettre la progression vers la phase tâches

**Acceptance Criteria similaires aux autres stubs**

#### US-011 : sdd_validate_tasks (Stub) (3 points)
**EN TANT QUE** serveur MCP  
**JE VEUX** valider la faisabilité des tâches  
**AFIN DE** permettre la progression vers l'exécution

#### US-012 : sdd_execute_task (Stub) (5 points)
**EN TANT QUE** utilisateur MCP  
**JE VEUX** exécuter une tâche spécifique  
**AFIN DE** progresser dans l'implémentation

**Acceptance Criteria :**
- Marque tâche comme "exécutée" dans state avec timestamp
- Interface compatible Phase 2 pour exécution réelle
- Logs détaillés pour debugging et audit
- Support simulation progression pour démonstrations

#### US-013 : Documentation Complète (8 points)
**EN TANT QUE** utilisateur du serveur MCP  
**JE VEUX** une documentation complète et à jour  
**AFIN DE** comprendre et utiliser efficacement le système

**Acceptance Criteria :**
- README détaillé avec exemples usage et quick start
- Documentation API MCP avec schémas JSON et exemples
- Guide déploiement et configuration production
- Architecture documentation mise à jour avec diagrammes
- Troubleshooting guide avec solutions problèmes courants

#### US-014 : Déploiement Docker (13 points)
**EN TANT QUE** équipe DevOps  
**JE VEUX** pouvoir déployer le serveur MCP facilement  
**AFIN D'** avoir un environnement reproductible

**Acceptance Criteria :**
- Dockerfile optimisé multi-stage pour production
- Docker Compose pour environnement développement
- Variables environnement configurables
- Health checks intégrés pour monitoring
- Scripts déploiement automatisés avec rollback

### PLANNING DÉTAILLÉ SPRINT 3

#### SEMAINE 6
**Lundi :** Sprint Planning (2h) + Architecture stubs (Tech Lead)
- Planning Sprint 3 avec focus finalisation
- Architecture stubs évolutive pour migration Phase 2
- Patterns et interfaces stables

**Mardi :** Implémentation 4 stubs MCP (Développeur Senior)
- Développement 4 stubs avec logging et métriques
- Tests unitaires et validation interfaces
- Documentation stubs avec migration path

**Mercredi :** Tests intégration stubs + workflow complet (DevOps + Développeur Senior)
- Intégration stubs dans workflow existant
- Tests workflow complet avec stubs
- Validation non-regression

**Jeudi :** Documentation API et architecture (Tech Lead + Product Owner)
- Documentation API MCP détaillée
- Architecture documentation avec diagrammes
- README et guides utilisateur

**Vendredi :** Dockerfile et containerization (DevOps)
- Dockerfile optimisé production
- Configuration environnements
- Tests containerisation

#### SEMAINE 7
**Lundi :** Docker Compose et scripts déploiement (DevOps)
- Docker Compose environnement complet
- Scripts déploiement et maintenance
- Documentation déploiement

**Mardi :** Documentation utilisateur et guides (Product Owner + Tech Lead)
- Guide utilisateur avec exemples concrets
- Troubleshooting guide
- Documentation configuration

**Mercredi :** Tests end-to-end complets + performance (Équipe complète)
- Tests e2e workflow complet
- Tests performance et charge
- Validation compatibilité clients MCP

**Jeudi :** Polissage, fixes finaux et optimisation (Équipe complète)
- Résolution derniers bugs
- Optimisations performance
- Finalisation documentation

**Vendredi :** Sprint Review/Demo MVP + rétrospective Phase 1 (Équipe complète)
- Démonstration MVP complet
- Validation finale Product Owner
- Rétrospective Phase 1 complète

### ARCHITECTURE STUBS ÉVOLUTIVE

```typescript
// Pattern stub compatible migration Phase 2
export abstract class BaseValidator {
  protected logger: Logger;
  
  constructor(
    protected config: ValidationConfig,
    logger: Logger
  ) {
    this.logger = logger.child({ component: this.constructor.name });
  }
  
  async validate(content: string, context: ValidationContext): Promise<ValidationResult> {
    // Logs et métriques communes
    this.logValidationAttempt(content, context);
    
    const startTime = Date.now();
    
    try {
      const result = await this.validateInternal(content, context);
      
      // Métriques succès
      this.recordMetrics('validation_success', Date.now() - startTime);
      
      return result;
    } catch (error) {
      // Métriques erreur
      this.recordMetrics('validation_error', Date.now() - startTime);
      throw error;
    }
  }
  
  protected abstract validateInternal(
    content: string, 
    context: ValidationContext
  ): Promise<ValidationResult>;
  
  private logValidationAttempt(content: string, context: ValidationContext): void {
    this.logger.info('Validation attempt', {
      contentLength: content.length,
      projectId: context.projectId,
      phase: context.phase
    });
  }
  
  private recordMetrics(type: string, duration: number): void {
    // Intégration future avec système métriques
    this.logger.debug('Validation metrics', { type, duration });
  }
}

// Stub Phase 1 - Toujours valide
export class RequirementsValidatorStub extends BaseValidator {
  protected async validateInternal(
    content: string, 
    context: ValidationContext
  ): Promise<ValidationResult> {
    return {
      valid: true,
      message: 'Stub validation - Phase 1 MVP always passes',
      score: 1.0,
      suggestions: []
    };
  }
}

// Migration Phase 2 - Vraie validation
export class RequirementsValidatorReal extends BaseValidator {
  protected async validateInternal(
    content: string,
    context: ValidationContext
  ): Promise<ValidationResult> {
    // Vraie logique validation métier
    const userStories = this.extractUserStories(content);
    const acceptanceCriteria = this.extractAcceptanceCriteria(content);
    
    const validationErrors = [
      ...this.validateUserStories(userStories),
      ...this.validateAcceptanceCriteria(acceptanceCriteria)
    ];
    
    return {
      valid: validationErrors.length === 0,
      message: validationErrors.length > 0 
        ? `${validationErrors.length} validation errors found`
        : 'Requirements validation passed',
      score: this.calculateScore(validationErrors),
      suggestions: this.generateSuggestions(validationErrors)
    };
  }
}
```

### CONTENEURISATION PRODUCTION

```dockerfile
# Dockerfile multi-stage optimisé
FROM node:18-alpine AS builder

# Install build dependencies
RUN apk add --no-cache python3 make g++

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src/ ./src/
COPY templates/ ./templates/

# Build application
RUN npm run build

# Production stage
FROM node:18-alpine AS runtime

# Create non-root user
RUN addgroup -g 1001 -S sddmcp && \
    adduser -S sddmcp -u 1001

# Install runtime dependencies
RUN apk add --no-cache \
    curl \
    tini

WORKDIR /app

# Copy built application
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/templates ./templates
COPY --chown=sddmcp:sddmcp package*.json ./

# Create data directory
RUN mkdir -p /app/data && chown sddmcp:sddmcp /app/data

# Switch to non-root user
USER sddmcp

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Use tini as init system
ENTRYPOINT ["/sbin/tini", "--"]

# Start application
CMD ["node", "dist/index.js"]
```

```yaml
# docker-compose.yml pour développement
version: '3.8'

services:
  mcp-sdd-server:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - PORT=3000
      - DATA_PATH=/app/data
    volumes:
      - ./data:/app/data
      - ./templates:/app/templates
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    
volumes:
  data:
    driver: local
```

### TESTS FINAUX CRITIQUES

#### Tests Workflow SDD Complet
```typescript
describe('Complete SDD Workflow with Stubs', () => {
  test('full workflow NEW → COMPLETED with all 8 tools', async () => {
    // 1. Initialize project
    const project = await executeWorkflow({
      name: 'Complete Test Project',
      phases: ['requirements', 'design', 'tasks', 'execution', 'completed']
    });
    
    // 2. Verify all phases completed
    expect(project.currentPhase).toBe('completed');
    expect(project.artifacts).toHaveProperty('requirements');
    expect(project.artifacts).toHaveProperty('design');
    expect(project.artifacts).toHaveProperty('tasks');
    
    // 3. Verify audit trail
    const history = await stateRepository.getProjectHistory(project.id);
    expect(history).toHaveLength(5); // One transition per phase
  });
  
  test('validation stubs always pass', async () => {
    const phases = ['requirements', 'design', 'tasks'];
    
    for (const phase of phases) {
      const result = await toolRegistry.execute(`sdd_validate_${phase}`, {
        projectId: 'test-project',
        content: 'minimal content'
      });
      
      expect(result.content[0].data.valid).toBe(true);
    }
  });
});
```

#### Tests Performance Globaux
```typescript
describe('Performance Tests', () => {
  test('tool execution under 200ms', async () => {
    const tools = [
      'sdd_project_init',
      'sdd_project_status', 
      'sdd_spec_create',
      'sdd_phase_advance'
    ];
    
    for (const toolName of tools) {
      const startTime = Date.now();
      await toolRegistry.execute(toolName, getValidParams(toolName));
      const duration = Date.now() - startTime;
      
      expect(duration).toBeLessThan(200);
    }
  });
  
  test('concurrent requests handling', async () => {
    const concurrentRequests = Array(10).fill().map(() =>
      toolRegistry.execute('sdd_project_status', { projectId: 'test-project' })
    );
    
    const results = await Promise.all(concurrentRequests);
    
    // All requests should succeed
    results.forEach(result => {
      expect(result.isError).toBeFalsy();
    });
  });
});
```

### MÉTRIQUES SUCCÈS MVP

#### Métriques Fonctionnelles
- [x] 100% user stories completed et validées Product Owner
- [x] Workflow SDD end-to-end fonctionnel (NEW → COMPLETED)
- [x] 8 outils MCP implémentés (4 fonctionnels + 4 stubs)
- [x] Compatibilité clients MCP validée (Claude, Cursor)

#### Métriques Techniques
- [x] Tests automatisés passent (>85% coverage globale)
- [x] Performance acceptable (<200ms par outil, <500ms e2e)
- [x] Déploiement Docker fonctionnel avec health checks
- [x] Documentation complète et à jour

#### Métriques Qualité
- [x] Code review 100% avec validation Tech Lead
- [x] Architecture évolutive pour Phase 2
- [x] Logs et monitoring opérationnels
- [x] Zero breaking changes pour migration future

### LIVRABLES FINAUX PHASE 1

#### Fonctionnalités
- [x] **Serveur MCP complet** avec 8 outils (4 fonctionnels + 4 stubs)
- [x] **Workflow SDD end-to-end** opérationnel et validé
- [x] **Template Engine** avec templates personnalisables
- [x] **State Manager** robuste avec persistence fichiers

#### Infrastructure
- [x] **Déploiement Docker** prêt production avec optimisations
- [x] **CI/CD pipeline** complet avec tests automatisés
- [x] **Monitoring** basique avec health checks et logs
- [x] **Documentation** complète (technique + utilisateur)

#### Architecture Évolutive
- [x] **Interfaces stables** pour migration Phase 2 (PostgreSQL)
- [x] **Stubs compatibles** pour remplacement validation réelle
- [x] **Tests non-regression** pour validation migration
- [x] **Documentation migration** disponible

### TRANSITION VERS PHASE 2

#### Préparation Migration
- Architecture compatible migration PostgreSQL sans breaking changes
- Interfaces `IStateRepository` et `IWorkflowManager` stables
- Documentation migration détaillée avec scripts
- Tests regression pour validation migration

#### Roadmap Phase 2
- Migration State Manager vers PostgreSQL
- Remplacement stubs par validation métier réelle
- Ajout Prompt Orchestrator intelligent
- Optimisations performance et cache Redis

---

## MÉTRIQUES GLOBALES PHASE 1

### Métriques de Livraison
- **Délai** : 6 semaines exactement (3 sprints de 2 semaines)
- **Scope** : 100% fonctionnalités MVP livrées
- **Qualité** : >85% test coverage, 0 bugs critiques
- **Performance** : <200ms latence outils, <500ms workflow e2e

### Métriques Techniques
- **Architecture** : 5 interfaces stables, 8 outils MCP
- **Tests** : >200 tests automatisés (unitaires + intégration + e2e)
- **Documentation** : 100% APIs documentées, guides complets
- **Déploiement** : Docker production-ready avec CI/CD

### Métriques Adoption
- **Compatibilité** : Claude, Cursor et autres clients MCP
- **Utilisabilité** : Workflow SDD complet fonctionnel
- **Évolutivité** : Architecture prête pour Phases 2 et 3
- **Maintenance** : Monitoring et debugging opérationnels

---

## CONCLUSION PHASE 1

La Phase 1 MVP fournit une base solide et fonctionnelle pour le serveur MCP SDD, permettant aux équipes de développement de commencer à utiliser le workflow SDD immédiatement. L'architecture évolutive garantit une migration transparente vers les phases suivantes pour ajouter persistence robuste, validation métier réelle et fonctionnalités avancées.

**READY FOR PHASE 2 - CONSOLIDATION**
