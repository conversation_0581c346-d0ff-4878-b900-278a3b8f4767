---
mode: agent
model: <PERSON> 4
tools: ['codebase', 'search', 'fetch']
description: Génère les spécifications SDD (requirements.md, design.md, tasks.md) en séquence automatique sans validation utilisateur.
---

# Génération des Spécifications SDD

Tu vas maintenant exécuter le processus de génération des spécifications SDD pour la fonctionnalité **${input}** selon la méthodologie Spec-Driven Development.

## Workflow de Spécification

### Règle Fondamentale
**Génération séquentielle Requirements → Design → Tasks.**

### Étapes Séquentielles

#### 1. Phase Requirements (requirements.md)

**Génération :**
- **<PERSON><PERSON><PERSON> le fichier** `.sdd/specs/${input}/requirements.md`
- **Appliquer le format EARS strict** : "The system SHALL/SHOULD/MAY..."
- **Générer une version complète** basée sur l'idée utilisateur SANS questions séquentielles préalables
- **Inclure** :
  - User stories au format : "En tant que [rôle], je veux [fonctionnalité], afin que [bénéfice]"
  - Critères d'acceptation au format EARS
  - Prise en compte des cas limites, UX, contraintes techniques

#### 2. Phase Design (design.md)

**Prérequis :** S'assurer que requirements.md existe

**Génération :**
- **Créer le fichier** `.sdd/specs/${input}/design.md`
- **Effectuer des recherches nécessaires** PENDANT le processus (pas de fichiers séparés)
- **Intégrer les résultats** de recherche directement dans le design
- **Inclure diagrammes Mermaid** si applicable
- **Mettre en avant** les décisions de design et leurs justifications
- **S'assurer** que le design répond à toutes les exigences identifiées

#### 3. Phase Tasks (tasks.md)

**Prérequis :** S'assurer que design.md existe

**Génération :**
- **Créer le fichier** `.sdd/specs/${input}/tasks.md`
- **Convertir le design** en série de tâches de codage orientées test
- **Format checklist** avec maximum 2 niveaux de hiérarchie
- **Chaque tâche** doit :
  - Être actionnable par un agent de codage
  - Référencer des exigences spécifiques du requirements.md
  - S'appuyer de manière incrémentale sur les précédentes
  - Inclure uniquement des tâches de codage (écriture, modification, test de code)

**Instructions de Conversion :**
```
Convertir le design en une série de prompts pour un LLM de génération de code qui implémentera chaque étape de manière orientée test. Prioriser les bonnes pratiques, la progression incrémentale et les tests précoces. Chaque prompt doit s'appuyer sur les précédents et finir par tout relier ensemble. Pas de code orphelin non intégré. Se concentrer UNIQUEMENT sur les tâches impliquant l'écriture, la modification ou le test de code.
```

**Exclusions Obligatoires :**
- Tests d'acceptation utilisateur ou collecte de retours
- Déploiement en production ou préproduction
- Collecte ou analyse de métriques de performance
- Formation utilisateur ou création de documentation
- Changements de processus métier
- Activités marketing ou de communication

## Contraintes Techniques

### Structure des Fichiers
- **Chemin obligatoire** : `.sdd/specs/{feature_name}/`
- **Nommage** : kebab-case pour les noms de fonctionnalités

### Formats Spécifiques
- **EARS** : Format "The system SHALL/SHOULD/MAY..." pour requirements
- **Mermaid** : Diagrammes intégrés pour design si applicable
- **Checklist** : Format checkbox pour tasks avec références aux requirements

## Principes de Génération

### Qualité et Cohérence
- **Traçabilité** : Maintenir les liens entre requirements → design → tasks
- **Complétude** : Couvrir tous les aspects de la fonctionnalité
- **Cohérence** : Assurer l'alignement entre tous les documents
- **Actionnable** : Tâches directement implémentables

### Méthodologie SDD
- **Spec-driven** : Les spécifications guident l'implémentation
- **Incrémental** : Progression logique et construite
- **Test-oriented** : Focus sur la testabilité et validation
- **Documentation** : Traçabilité complète des décisions

### Finalisation
Après génération du document tasks :
```
**Workflow de planification terminé !**

Vous pouvez maintenant commencer l'implémentation en utilisant l'agent .sdd-implementation pour exécuter les tâches une par une selon la progression définie.
```

## Instructions d'Exécution

**COMMENCER MAINTENANT** avec la génération des spécifications pour la fonctionnalité ${input} :

1. **Génération Requirements** : Créer requirements.md complet avec format EARS
2. **Génération Design** : Créer design.md avec recherche intégrée et justifications
3. **Génération Tasks** : Créer tasks.md avec tâches de codage orientées test
4. **Finalisation** : Confirmer la completion du workflow

**Objectif** : Transformer l'idée de fonctionnalité en spécifications complètes exploitables selon la méthodologie SDD, avec génération automatique des 3 documents sans interruption.